name: Feature request
description: Suggest an idea for this project
type: feature
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to suggest how we can make this project even better!
  - type: dropdown
    id: component
    attributes:
      label: Component
      description: First, tell us which component we should focus on for this request
      options:
        - I don't know
        - Quickstart
        - Helm Chart
        - Other
      default: 0
    validations:
      required: true
  - type: textarea
    id: describe
    attributes:
      label: Desired use case or feature
      description: Is your feature request related to a problem? Please describe
      placeholder: A clear and concise description of what the problem is. Ex. I have the following use-case [...]. For this use-case, I would like the `llm-d-deployer` to [...]
    validations:
      required: true
  - type: textarea
    id: proposal
    attributes:
      label: Proposed solution
      description: Describe the solution approach you'd like
      placeholder: A clear and concise description of what you want to happen
    validations:
      required: true
  - type: textarea
    id: alternatives
    attributes:
      label: Alternatives
      description: Describe alternatives you've considered
      placeholder: A clear and concise description of any alternative solutions or features you've considered
  - type: textarea
    id: extra
    attributes:
      label: Additional context or screenshots
      description: Add any other context about the problem here
      placeholder: Anything else you want to say to the report, attach screenshots, this is the place
