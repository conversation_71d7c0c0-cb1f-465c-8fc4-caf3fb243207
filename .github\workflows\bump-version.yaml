name: Bump

on:
  issue_comment:
    types: [created]

jobs:
  chart-version:
    name: Chart Version
    runs-on: ubuntu-latest
    if: ${{ github.event.issue.pull_request }}

    permissions:
      contents: write
      id-token: write
      issues: write
      pull-requests: write

    steps:
      - name: Check for command
        id: command
        continue-on-error: true
        uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7.0.1
        with:
          script: |
            const commentBody = context.payload.comment.body;
            const commandPrefix = "/bump ";
            let commandName = "";
            let commandArgs = "";

            if (commentBody.startsWith(commandPrefix)) {
              commandName = "bump";
              const args = commentBody.slice(commandPrefix.length).trim().split(" ");
              commandArgs = args[0] || "patch";
            }

            core.setOutput("command-name", commandName);
            core.setOutput("command-arguments", commandArgs);

      - name: Add eyes reaction
        if: steps.command.outputs.command-name == 'bump'
        uses: peter-evans/create-or-update-comment@71345be0265236311c031f5c7866368bd1eff043 # v4.0.0
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          repository: ${{ github.repository }}
          comment-id: ${{ github.event.comment.id }}
          reactions: eyes

      - uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5
        if: steps.command.outputs.command-name == 'bump'
        with:
          python-version: 3.13

      - uses: actions/setup-go@0aaccfd150d50ccaeb58ebd88d36e91967a5f35b # v5
        if: steps.command.outputs.command-name == 'bump'
        with:
          go-version: ^1

      - name: Setup helm-docs
        if: steps.command.outputs.command-name == 'bump'
        run: go install github.com/norwoodj/helm-docs/cmd/helm-docs@latest

      - name: Generate token
        if: steps.command.outputs.command-name == 'bump'
        id: generate_token
        uses: tibdex/github-app-token@3beb63f4bd073e61482598c45c71c1019b59b73a # v2
        with:
          app_id: "1229152"
          private_key: ${{ secrets.BUMPER_GITHUB_APP_PRIVATE_KEY }}

      - name: Checkout Repository
        if: steps.command.outputs.command-name == 'bump'
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          token: ${{ steps.generate_token.outputs.token }}
          persist-credentials: true

      - name: Checkout Pull Request
        if: steps.command.outputs.command-name == 'bump'
        run: gh pr checkout ${{ github.event.issue.number }}
        env:
          GITHUB_TOKEN: ${{ steps.generate_token.outputs.token }}

      - name: Get version
        if: steps.command.outputs.command-name == 'bump'
        id: get_version
        uses: mikefarah/yq@8bf425b4d1344db7cd469a8d10a390876e0c77fd # v4.45.1
        with:
          cmd: yq ".version" charts/llm-d/Chart.yaml

      - uses: actions-ecosystem/action-bump-semver@34e334551143a5301f38c830e44a22273c6ff5c5  # v1
        if: steps.command.outputs.command-name == 'bump'
        id: semver
        with:
          current_version: ${{ steps.get_version.outputs.result }}
          level: ${{ steps.command.outputs.command-arguments }}

      - name: Bump the version
        if: steps.command.outputs.command-name == 'bump'
        uses: mikefarah/yq@8bf425b4d1344db7cd469a8d10a390876e0c77fd # v4.45.1
        with:
          cmd: yq -i '.version = "${{ steps.semver.outputs.new_version }}"' charts/llm-d/Chart.yaml

      - name: Run pre-commit
        if: steps.command.outputs.command-name == 'bump'
        uses: pre-commit/action@2c7b3805fd2a0fd8c1884dcaebf91fc102a13ecd # v3.0.1
        continue-on-error: true

      - name: Setup Gitsign
        if: steps.command.outputs.command-name == 'bump'
        uses: chainguard-dev/actions/setup-gitsign@be6c67b5b374ed43d908ac017ff9b04c271ad3d8 # v1.0.3

      - name: Commit pre-commit changes
        if: steps.command.outputs.command-name == 'bump'
        uses: stefanzweifel/git-auto-commit-action@b863ae1933cb653a53c021fe36dbb774e1fb9403 # v5
        env:
          NEW_VERSION: ${{ steps.semver.outputs.new_version }}
        with:
          commit_message: Bump version to $NEW_VERSION
          commit_options: '-s'
          commit_user_name: BUMPER bot (llm-d)
          commit_user_email: <EMAIL>
          commit_author: BUMPER bot (llm-d) <<EMAIL>>
