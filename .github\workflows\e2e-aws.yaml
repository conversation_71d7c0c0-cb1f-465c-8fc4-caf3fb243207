name: Quickstart e2e on AWS

on:
  workflow_dispatch:
    inputs:
      aws_instance_type:
        description: 'ec2 instance type (default g6e.12xlarge): g6e.2xlarge | g6e.8xlarge | g6e.16xlarge'
        required: true
        default: 'g6e.12xlarge'
        type: string
      pr_or_branch:
        description: 'Pull-request number or branch name to test'
        required: true
        default: 'main'
        type: string
      disk_size:
        description: 'AWS instance disk size?'
        required: true
        default: "300"
        type: string
      wait_for_pods:
        description: 'Max wait time (in minutes) for pods to get ready:'
        required: true
        default: 10
        type: number
      wait_for_termination:
        description: 'Wait time (in minutes) before terminating AWS instance:'
        required: true
        default: 30
        type: number

permissions:
  contents: write
  actions: write
  packages: read

jobs:
  deploy-on-ec2:
    runs-on: ubuntu-latest

    env:
      INSTANCE_TYPE: ${{ github.event.inputs.aws_instance_type }}
      AMI_ID: ami-04f167a56786e4b09
      KEY_NAME: ${{ secrets.SSH_KEY_NAME }}
      REGION: ${{ secrets.AWS_REGION }}
      HF_TOKEN: ${{secrets.HF_TOKEN}}
      TERMINATION_TIMEOUT: ${{ github.event.inputs.wait_for_termination }}
      PODS_READINESS_TIMEOUT: ${{ github.event.inputs.wait_for_pods }}
      DISK_SIZE: ${{ github.event.inputs.disk_size }}
      PR_OR_BRANCH: ${{ github.event.inputs.pr_or_branch }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Determine if pr_or_branch is a PR number
        id: check_pr
        run: |
          if [[ "$PR_OR_BRANCH" =~ ^[0-9]+$ ]]; then
            echo "is_pr=true" >> "$GITHUB_OUTPUT"
          else
            echo "is_pr=false" >> "$GITHUB_OUTPUT"
          fi

      - name: Fetch and checkout PR
        if: steps.check_pr.outputs.is_pr == 'true'
        run: |
          git fetch origin pull/"$PR_OR_BRANCH"/head:pr-"$PR_OR_BRANCH"
          git checkout pr-"$PR_OR_BRANCH"

      - name: Checkout branch
        if: steps.check_pr.outputs.is_pr == 'false'
        run: git checkout "$PR_OR_BRANCH"

      - name: Install AWS CLI
        run: |
          sudo apt-get update
          curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
          unzip awscliv2.zip
          sudo ./aws/install --update

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@f24d7193d98baebaeacc7e2227925dd47cc267f5
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Launch EC2 instance
        id: launch
        run: |
          INSTANCE_ID=$(aws ec2 run-instances \
            --image-id $AMI_ID \
            --count 1 \
            --instance-type $INSTANCE_TYPE \
            --key-name $KEY_NAME \
            --block-device-mappings "[{
                \"DeviceName\": \"/dev/sda1\",
                \"Ebs\": {
                  \"VolumeSize\": ${DISK_SIZE},
                  \"VolumeType\": \"gp3\",
                  \"DeleteOnTermination\": true
                }
              }]" \
            --tag-specifications "ResourceType=instance,Tags=[{Key=Name,Value=llmd-github-ci-runner}]" \
            --query 'Instances[0].InstanceId' \
            --output text)

          echo "INSTANCE_ID=$INSTANCE_ID" >> $GITHUB_ENV

          echo "Waiting for instance to be running..."
          aws ec2 wait instance-running --instance-ids $INSTANCE_ID

          PUBLIC_IP=$(aws ec2 describe-instances \
            --instance-ids $INSTANCE_ID \
            --query "Reservations[0].Instances[0].PublicIpAddress" \
            --output text)

          echo "INSTANCE_IP=$PUBLIC_IP" >> $GITHUB_ENV
          SECURITY_GROUP_ID=$(aws ec2 describe-instances \
            --instance-ids $INSTANCE_ID \
            --query "Reservations[0].Instances[0].SecurityGroups[0].GroupId" \
            --output text)

          echo "Authorizing SSH in security group $SECURITY_GROUP_ID..."

          aws ec2 authorize-security-group-ingress \
            --group-id $SECURITY_GROUP_ID \
            --protocol tcp \
            --port 22 \
            --cidr 0.0.0.0/0 || echo "SSH rule may already exist — continuing"

      - name: Wait for SSH to be ready
        run: |
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > key.pem
          chmod 600 key.pem

          echo "Waiting for SSH on $INSTANCE_IP..."
          for i in {1..30}; do
            ssh -o StrictHostKeyChecking=no -i key.pem ubuntu@$INSTANCE_IP "echo connected" && break
            sleep 10
          done

      - name: Setup installer pre-requisites
        id: setup-pre-requisite
        run: |
          ssh -o StrictHostKeyChecking=no -i key.pem ubuntu@$INSTANCE_IP << EOF
            set -e
            sudo apt-get update -y
            sudo apt-get install -y git

            git clone https://github.com/llm-d/llm-d-deployer.git
            cd llm-d-deployer/quickstart

            ./install-deps.sh | tee ~/install-deps.log
          EOF

      - name: Setup docker engine
        id: setup-docker
        run: |
          ssh -o StrictHostKeyChecking=no -i key.pem ubuntu@$INSTANCE_IP << EOF
            set -e
            sudo apt-get -y install ca-certificates curl
            sudo install -m 0755 -d /etc/apt/keyrings
            sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc
            sudo chmod a+r /etc/apt/keyrings/docker.asc

            echo \
              "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu \
              $(. /etc/os-release && echo "${UBUNTU_CODENAME:-$VERSION_CODENAME}") stable" | \
              sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

              sudo apt-get update

              sudo apt-get -y install docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

              sudo usermod -aG docker ubuntu
              mkdir -p ~/.config/containers/
          EOF

      - name: Copy docker auth configuration file
        id: docker-auth
        run: |
          echo "${{ secrets.CR_AUTH_JSON }}" > auth.json
          chmod +x auth.json
          rsync -avz -e "ssh -o StrictHostKeyChecking=no -i key.pem" auth.json ubuntu@$INSTANCE_IP:~/
          ssh -o StrictHostKeyChecking=no -i key.pem ubuntu@$INSTANCE_IP << EOF
          mv ~/auth.json ~/.config/containers/
          EOF

      - name: Setup nvidia cuda toolkit
        id: setup-cuda-toolkit
        run: |
          ssh -o StrictHostKeyChecking=no -i key.pem ubuntu@$INSTANCE_IP << EOF
            set -e
            wget https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2404/x86_64/cuda-keyring_1.1-1_all.deb
            sudo dpkg -i cuda-keyring_1.1-1_all.deb
            sudo apt-get update
            sudo apt-get -y install cuda-toolkit-12-8

            sudo apt-get install -y nvidia-open nvtop nload
          EOF

      - name: Reboot the aws instance
        id: reboot-instance
        run: |
          echo "Rebooting instance..."
          aws ec2 reboot-instances --instance-ids $INSTANCE_ID
          sleep 60
          echo "Waiting for instance to become healthy again..."
          aws ec2 wait instance-status-ok --instance-ids $INSTANCE_ID

      - name: Wait for SSH to be ready
        run: |
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > key.pem
          chmod 600 key.pem

          echo "Waiting for SSH on $INSTANCE_IP..."
          for i in {1..30}; do
            ssh -o StrictHostKeyChecking=no -i key.pem ubuntu@$INSTANCE_IP "echo connected" && break
            sleep 10
          done

      - name: Setup nvidia container toolkit
        id: setup-container-toolkit
        run: |
          ssh -o StrictHostKeyChecking=no -i key.pem ubuntu@$INSTANCE_IP << EOF
            set -e
            curl -fsSL https://nvidia.github.io/libnvidia-container/gpgkey | sudo gpg --dearmor -o /usr/share/keyrings/nvidia-container-toolkit-keyring.gpg \
              && curl -s -L https://nvidia.github.io/libnvidia-container/stable/deb/nvidia-container-toolkit.list | \
                sed 's#deb https://#deb [signed-by=/usr/share/keyrings/nvidia-container-toolkit-keyring.gpg] https://#g' | \
                sudo tee /etc/apt/sources.list.d/nvidia-container-toolkit.list

            sudo apt-get update
            sudo apt-get install -y nvidia-container-toolkit

            sudo sysctl net.core.bpf_jit_harden
            echo "net.core.bpf_jit_harden=0" | sudo tee -a /etc/sysctl.conf
            sudo sysctl -p
            sudo nvidia-ctk runtime configure --runtime=docker && sudo systemctl restart docker
          EOF

      - name: Install minikube
        id: install-minikube
        run: |
          ssh -o StrictHostKeyChecking=no -i key.pem ubuntu@$INSTANCE_IP << EOF
            set -e
            curl -LO https://github.com/kubernetes/minikube/releases/latest/download/minikube-linux-amd64
            sudo install minikube-linux-amd64 /usr/local/bin/minikube && rm minikube-linux-amd64
          EOF

      - name: Run installer to deploy minikube-gpu and llm-d stack
        id: run-installer
        run: |
          ssh -o StrictHostKeyChecking=no -i key.pem ubuntu@$INSTANCE_IP << EOF
            set -e
            cd llm-d-deployer/quickstart
            echo "Starting minikube with gpu support enabled..."
            minikube start --driver docker --container-runtime docker --gpus all --memory no-limit
            sleep 10
            echo "Deploying llm-d stack..."
            export HF_TOKEN=$HF_TOKEN
            ./llmd-installer.sh --minikube | tee ~/llmd-installer.log
          EOF

      - name: Wait for pods to be in ready state.
        id: pod-readiness
        run: |
          echo "Waiting for pods to come to ready state"
          for i in $(seq 1 $PODS_READINESS_TIMEOUT); do
            echo "Attempt $i to check pod readiness..."
            ssh -o StrictHostKeyChecking=no -i key.pem ubuntu@$INSTANCE_IP \
              kubectl get pods -n llm-d --no-headers > pods.txt
            cat pods.txt
            NOT_READY_COUNT=$(awk '{ split($2, parts, "/"); if (parts[1] != parts[2]) count++ } END { print count + 0}' pods.txt)
            if [ "$NOT_READY_COUNT" -eq 0 ]; then
              echo "✅ All pods are ready."
              exit 0
            else
              echo "❌ $NOT_READY_COUNT pods are not ready yet."
            fi
            sleep 60
          done

          echo "❌ Pods are still not ready after $PODS_READINESS_TIMEOUT minutes."
          exit 1

      - name: Quick inference test
        id: inference-test
        if: steps.pod-readiness.outcome == 'success'
        run: |
          set -x
          ssh -o StrictHostKeyChecking=no -i key.pem ubuntu@$INSTANCE_IP << EOF
            set -ex
            cd llm-d-deployer/.github/scripts/e2e/
            export MODEL_ID="Llama-3.2-3B-Instruct"
            ./e2e-validate.sh
          EOF

      - name: Collect and upload Kubernetes pod logs
        id: collect-logs
        if: always()
        run: |
          echo "Collecting logs from pods in llm-d namespace..."
          ssh -o StrictHostKeyChecking=no -i key.pem ubuntu@$INSTANCE_IP << EOF
            mkdir -p pod-logs
            echo "Fetching llm-d pods log..."
            kubectl get pods -n llm-d --no-headers -o custom-columns=":metadata.name" \
            | xargs -I{} sh -c 'kubectl logs --all-containers=true -n llm-d {} > "pod-logs/{}.log" 2>&1'

            echo "Fetching llm-d pods descriptions..."
            kubectl get pods -n llm-d --no-headers -o custom-columns=":metadata.name" \
            | xargs -I{} sh -c 'kubectl describe pod -n llm-d {} > "pod-logs/{}-describe.log" 2>&1'

            mv ~/llmd-installer.log pod-logs
            mv ~/install-deps.log pod-logs
            tar -czf pod-logs.tar.gz pod-logs
          EOF

          scp -o StrictHostKeyChecking=no -i key.pem ubuntu@$INSTANCE_IP:pod-logs.tar.gz .

          mkdir -p extracted-logs
          tar -xzf pod-logs.tar.gz -C extracted-logs
          echo "✅ Logs downloaded from the AWS instance."

      - name: Upload pod logs as artifact
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: llmd-pod-logs
          path: extracted-logs

      - name: Wait if script failed
        if: always()
        run: |
          echo "llm-d Installation failed. Waiting for $TERMINATION_TIMEOUT minutes before terminating AWS instance"
          sleep $((TERMINATION_TIMEOUT * 60))

      - name: Terminate EC2 instance
        if: always()
        run: |
          aws ec2 terminate-instances --instance-ids $INSTANCE_ID
          aws ec2 wait instance-terminated --instance-ids $INSTANCE_ID
