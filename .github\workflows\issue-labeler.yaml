name: Issue labeler
on:
  issues:
    types:
      - opened

permissions:
  contents: read

jobs:
  label-component:
    runs-on: ubuntu-latest

    permissions:
      # required for all workflows
      issues: write

      # only required for workflows in private repositories
      actions: read
      contents: read

    strategy:
      matrix:
        template: [bug, feature]

    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          persist-credentials: false

      - name: Parse issue form
        uses: stefanbuck/github-issue-parser@2ea9b35a8c584529ed00891a8f7e41dc46d0441e # v3
        id: issue-parser
        with:
          template-path: .github/ISSUE_TEMPLATE/${{ matrix.template }}.yaml

      - name: Set labels based on severity field
        uses: redhat-plumbers-in-action/advanced-issue-labeler@39087a4b30cb98d57f25f34d617a6af8163c17d9 # v3
        with:
          issue-form: ${{ steps.issue-parser.outputs.jsonString }}
          section: component
          token: ${{ secrets.GITHUB_TOKEN }}
