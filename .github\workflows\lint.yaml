name: <PERSON>t Charts

on:
  pull_request:
    branches:
      - main

concurrency:
  group: ${{ github.workflow }}-${{ github.event.number }}
  cancel-in-progress: true

permissions:
  pull-requests: read
  contents: read

jobs:
  lint-chart:
    name: <PERSON><PERSON>ada<PERSON>
    runs-on: ubuntu-latest
    env:
      GO111MODULE: on

    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          fetch-depth: 0
          persist-credentials: true

      - name: Set up Helm
        uses: azure/setup-helm@5119fcb9089d432beecbf79bb2c7915207344b78 # renovate: tag=v3.5
        with:
          version: v3.10.0

      - name: Helm add bitnami chart
        run: |
          helm repo add bitnami https://charts.bitnami.com/bitnami
          helm repo update
          helm dependency build charts/llm-d

      - uses: actions/setup-python@8d9ed9ac5c53483de85588cdf95a591a75ab9f55 # v5
        with:
          python-version: 3.13

      - name: Set up chart-testing
        uses: helm/chart-testing-action@0d28d3144d3a25ea2cc349d6e59901c4ff469b3b # v2.7.0

      - name: Run chart-testing (list-changed)
        id: list-changed
        env:
          TARGET_BRANCH: ${{ github.event.pull_request.base.ref }}
        run: |
          changed=$(ct list-changed --target-branch "$TARGET_BRANCH")
          if [[ -n "$changed" ]]; then
            echo "changed=true" >> "$GITHUB_OUTPUT"
          fi

      - name: Run chart-testing (lint)
        if: steps.list-changed.outputs.changed == 'true'
        env:
          TARGET_BRANCH: ${{ github.event.pull_request.base.ref }}
        run: |
          ct lint \
            --debug \
            --config ct.yaml \
            --target-branch "$TARGET_BRANCH"
