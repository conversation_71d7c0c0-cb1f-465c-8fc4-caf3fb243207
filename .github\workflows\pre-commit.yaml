name: Pre-commit

on:
  pull_request:
    branches:
      - main

concurrency:
  group: ${{ github.workflow }}-${{ github.event.number }}
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  pre-commit:
    name: Pre-commit
    runs-on: ubuntu-latest
    env:
      GO111MODULE: on
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          persist-credentials: false

      - uses: actions/setup-python@8d9ed9ac5c53483de85588cdf95a591a75ab9f55 # v5
        id: python
        with:
          python-version: 3.13

      - uses: actions/setup-go@0aaccfd150d50ccaeb58ebd88d36e91967a5f35b # v5
        id: go
        with:
          go-version: ^1

      - name: Restore pre-commit cache
        id: cache-restore
        uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4
        with:
          path: |
            ~/.cache/pre-commit
            ~/.cache/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-py${{ steps.python.outputs.python-version }}-go${{ steps.go.outputs.go-version }}-${{ hashFiles('**/.pre-commit-config.yaml') }}

      - run: python -m pip install pre-commit
        shell: bash
      - run: python -m pip freeze --local
        shell: bash

      - name: Set up Helm
        uses: azure/setup-helm@5119fcb9089d432beecbf79bb2c7915207344b78 # renovate: tag=v3.5
        with:
          version: v3.10.0

      - name: Helm add bitnami chart
        run: |
          helm repo add bitnami https://charts.bitnami.com/bitnami
          helm repo update
          helm dependency build charts/llm-d

      - name: Setup helm-docs
        run: go install github.com/norwoodj/helm-docs/cmd/helm-docs@latest

      - name: Run pre-commit
        run: pre-commit run --show-diff-on-failure --color=always --verbose --all-files --show-diff-on-failure
        shell: bash
