name: PR Renovate Config Validator

on:
  pull_request:
    paths:
      - '.github/renovate.json'
    # Renovate always uses the config from the repository default branch
    # https://docs.renovatebot.com/configuration-options/
    branches:
      - main

permissions:
  contents: read

jobs:
  renovate-config-validator:
    runs-on: ubuntu-latest
    name: Renovate Config Validator
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
        with:
          persist-credentials: false

      - name: Validate config
        # See https://docs.renovatebot.com/config-validation/
        run: |
          npx --yes --package renovate -- renovate-config-validator --strict .github/renovate.json
