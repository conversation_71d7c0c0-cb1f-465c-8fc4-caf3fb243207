apiVersion: tekton.dev/v1
kind: PipelineRun
metadata:
  name: llm-d-deployer
  annotations:
    pipelinesascode.tekton.dev/on-event: "[pull_request, push]"
    pipelinesascode.tekton.dev/on-target-branch: "[main]"
    pipelinesascode.tekton.dev/task: "git-clone"
    pipelinesascode.tekton.dev/max-keep-runs: "3"
    pipelinesascode.tekton.dev/git-status: "true"
    pipelinesascode.tekton.dev/on-cel-expression: >
      (!has(body.ref) || body.ref == 'refs/heads/main') &&
      (!has(body.head_commit) || !has(body.head_commit.author) || !body.head_commit.author.name.matches("(?i).*ci-tag-bot.*")) &&
      (!has(body.pull_request) || (body.pull_request.base.ref == 'main'))
spec:
  podTemplate:
    serviceAccountName: pipeline
    securityContext:
      fsGroup: 0
    imagePullSecrets:
      - name: icr-secret
  params:
    - name: runOptional
      value: "true"
    - name: repo_url
      value: "{{ repo_url }}"
    - name: revision
      value: "{{ revision }}"
    - name: deleteExisting
      value: "true"
    - name: source_branch
      value: "{{ source_branch }}"
  pipelineSpec:
    params:
      - name: repo_url
      - name: revision
      - name: deleteExisting
      - name: source_branch
    workspaces:
      - name: source
      - name: basic-auth
      - name: git-auth
      - name: registry-secret
    tasks:
      - name: fix-permissions
        taskSpec:
          workspaces:
            - name: source
              workspace: source
          steps:
            - name: fix
              image: quay.io/projectquay/golang:1.24
              script: |
                #!/bin/sh
                echo "Fixing permissions on /workspace/source..."
                chmod -R 777 /workspace/source || true
        workspaces:
          - name: source
            workspace: source

      - name: which-branch
        taskRef:
          name: print-branch-task
        runAfter:
          - fix-permissions
        params:
          - name: source-branch
            value: "$(params.source_branch)"
        workspaces:
          - name: source
            workspace: source

      - name: fetch-repository
        taskRef:
          name: git-clone
        runAfter:
          - which-branch
        workspaces:
          - name: output
            workspace: source
          - name: basic-auth
            workspace: basic-auth
        params:
          - name: url
            value: $(params.repo_url)
          - name: revision
            value: $(params.revision)
          - name: deleteExisting
            value: "$(params.deleteExisting)"

      - name: helm-ct-lint
        when:
          - input: "$(params.runOptional)"
            operator: in
            values: ["true"]
        taskRef:
          name: helm-ct-lint-task
        runAfter:
          - fetch-repository
        params:
          - name: chart-name
            value: llm-d
        workspaces:
          - name: source
            workspace: source

  workspaces:
    - name: source
      volumeClaimTemplate:
        spec:
          accessModes:
            - ReadWriteOnce
          resources:
            requests:
              storage: 1Gi
    - name: basic-auth
      secret:
        secretName: "{{ git_auth_secret }}"
    - name: git-auth
      secret:
        secretName: "git-auth-secret-llm-d"
    - name: registry-secret
      secret:
        secretName: icr-secret
