StylesPath = .vale/styles
MinAlertLevel = suggestion
IgnoredScopes = code, tt, img, url, a, body.id
SkippedScopes = script, style, pre, figure, code, tt, blockquote, listingblock, literalblock
Packages = RedHat

[*]
BasedOnStyles = RedHat
TokenIgnores = ({{[^\n]+}}), (\x60[^\n\x60]+\x60), ([^\n]+=[^\n]*), (\+[^\n]+\+), (http[^\n]+\[)
RedHat.GitLinks = NO
RedHat.PascalCamelCase = NO
RedHat.CaseSensitiveTerms = NO
RedHat.ConfigMap = NO
RedHat.Definitions = NO
RedHat.Slash = NO
RedHat.Spacing = NO
RedHat.Spelling = NO
RedHat.TermsSuggestions = NO
RedHat.TermsWarnings = NO
RedHat.TermsErrors = NO


[**/NOTES.txt]
RedHat.Spacing = NO
