---
extends: substitution
ignorecase: true
level: suggestion
link: https://redhat-documentation.github.io/vale-at-red-hat/docs/main/reference-guide/contractions/
message: "Avoid contractions. Use '%s' rather than '%s.'"
# source: "https://redhat-documentation.github.io/supplementary-style-guide/#contractions"
action:
  name: replace
swap:
  "aren't": are not
  "can't": cannot
  "couldn't": could not
  "didn't": did not
  "doesn't": does not
  "don't": do not
  "hasn't": has not
  "haven't": have not
  "how'll": how will
  "how's": how is
  "isn't": is not
  "it'll": it will
  "it's": it is
  "shouldn't": should not
  "that'll": that will
  "that's": that is
  "they'll": they will
  "they're": they are
  "wasn't": was not
  "we'll": we will
  "we're": we are
  "we've": we have
  "weren't": were not
  "what'll": what will
  "what's": what is
  "when'll": when will
  "when's": when is
  "where'll": where will
  "where's": where is
  "who'll": who will
  "who's": who is
  "why'll": why will
  "why's": why is
  "won't": will not
