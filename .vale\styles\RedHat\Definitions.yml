---
extends: conditional
ignorecase: false
level: suggestion
link: https://redhat-documentation.github.io/vale-at-red-hat/docs/main/reference-guide/definitions/
message: "Define acronyms and abbreviations (such as '%s') on first occurrence if they're likely to be unfamiliar."
# source: "IBM - Abbreviations, p. 1"
# Ensures that the existence of 'first' implies the existence of 'second'.
first: '\b([A-Z]{3,5}s?)\b'
second: '\(([A-Z]{3,5}s?)\)'
# ... with the exception of these:
exceptions:
  - 'APIs?'
  - 'CPUs?'
  - 'CVEs?'
  - 'DVDs?'
  - 'GIDs?'
  - 'NULL'
  - 'PIDs?'
  - 'ROMs?'
  - 'RPMs?'
  - 'UIDs?'
  - 'URIs?'
  - 'URLs?'
  - ACPI
  - AIX
  - ARM
  - ASCII
  - ASP
  - AWS
  - BIND
  - BIOS
  - BMC
  - CD
  - CIDR
  - CLI
  - CNAME
  - CSS
  - CSV
  - CUPS
  - DEBUG
  - DHCP
  - DNF
  - DNS
  - DOM
  - DOS
  - DPDK
  - DPI
  - DRL
  - DSL
  - EIP
  - ELB
  - ERROR
  - FAQ
  - FQDN
  - FTP
  - GAS
  - GB
  - GCC
  - GCJ
  - GDB
  - GET
  - GIF
  - GIMP
  - GNOME
  - GNU
  - GNUPro
  - GPL
  - GPU
  - GRUB
  - GTK+
  - GUI
  - GUID
  - HP
  - HTML
  - HTTP
  - HTTPS
  - IAM
  - IBM
  - IDE
  - IOPS
  - IOV
  - IP
  - ISO
  - IT
  - JAR
  - JPEG
  - JSON
  - JSX
  - JVM
  - KB
  - KIE
  - KVM
  - LAN
  - LDAP
  - LESS
  - LLDB
  - LPAR
  - LUKS
  - LVM
  - MAC
  - MB
  - MBR
  - MDS
  - mTLS
  - NAT
  - NET
  - NFS
  - NGINX
  - NIC
  - NOTE
  - NTP
  - NVDA
  - OEM
  - OSS
  - OVN
  - OVS
  - PATH
  - PC
  - PDF
  - PHP
  - PIN
  - PNG
  - POSIX
  - POST
  - PPP
  - PROM
  - PTP
  - PV
  - PVC
  - PXE
  - QCOW2
  - QEMU
  - RADOS
  - RAID
  - RAM
  - RAN
  - RBAC
  - RBD
  - REPL
  - REST
  - RFC
  - RGW
  - RHEL
  - RSA
  - SAML
  - SAP
  - SCM
  - SCSI
  - SCSS
  - SDK
  - SFTP
  - SHA
  - SOCKS
  - SPEC
  - SQL
  - SSD
  - SSH
  - SSL
  - SSSD
  - SVG
  - SVN
  - SWAT
  - TBD
  - TCP
  - TLS
  - TODO
  - TTL
  - UDP
  - UEFI
  - UNIX
  - USB
  - UTC
  - UTF
  - UUID
  - VAR
  - VDO
  - VDSM
  - VGA
  - VLAN
  - VNC
  - VPN
  - WAN
  - WCA
  - XFS
  - XML
  - XSS
  - YAML
  - YUM
  - ZIP
  - ZTP
