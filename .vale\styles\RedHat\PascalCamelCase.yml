---
extends: existence
ignorecase: false
level: suggestion
scope: [list, sentence]
link: https://redhat-documentation.github.io/asciidoc-markup-conventions
message: "Consider wrapping this Pascal or Camel case term ('%s') in backticks."
# source: https://github.com/redhat-documentation/vale-at-red-hat/tree/main/.vale/styles/RedHat/PascalCamelCase.yml
tokens:
  #PascalCase
  - ([A-Z]+[a-z]+){2,}
  - ([A-Z]+[a-z]+[A-Z]+){1,}
  - ([A-Z]+[A-Z]+[a-z]+){1,}
  #camelCase
  - ([a-z]+)([A-Z]+[a-z]+){1,}
exceptions:
  - '\b[A-Z]{2,}s?'
  - 'vCPUs?'
  - 3scale
  - AGPLv
  - AMQ
  - API
  - AppStream
  - AsciiDoc
  - AssertJ
  - BaseOS
  - BitBucket
  - BlueStore
  - CaaS
  - camelCase
  - CapEx
  - CentOS
  - CephFS
  - CheckTree
  - ClassLoader
  - CloudForms
  - CodeReady
  - ConfigMaps?
  - ConnectX
  - Convert2RHEL
  - CoreOS
  - DaemonSet
  - DDoS
  - DevOps
  - DevWorkspace
  - DNSSec
  - eServer
  - eXpress
  - eXtenSion
  - FaaS
  - FCoE
  - FileStore
  - FireWire
  - FreeRADIUS
  - GbE
  - GBps
  - GiB
  - GitHub
  - GitLab
  - GitOps
  - GlusterFS
  - GNUPro
  - GnuTLS
  - GraalVM
  - GraphQL
  - GTID
  - HashBase
  - HdrHistogram
  - Helm
  - HyperShift
  - IaaS
  - IBoE
  - IconBurst
  - IdM
  - IKEv
  - InfiniBand
  - InnoDB
  - IntelliJ
  - IntelliSense
  - IPoIB
  - IPsec
  - IPv
  - ISeries
  - JavaScript
  - JBoss
  - JetBrains
  - JUnit
  - kBps
  - KiB
  - LangTags
  - LGPLv
  - libOSMesa
  - LibreOffice
  - libXNVCtrl
  - LightPulse
  - LinuxONE
  - LiquidIO
  - ManageIQ
  - MariaDB
  - MBps
  - MegaRAID
  - MiB
  - MicroProfile
  - MongoDB
  - MoreUtils
  - MySQL
  - NetBIOS
  - NetcoredebugOutput
  - NetWeaver
  - NetworkManager
  - NetXen
  - NetXtreme
  - NFSv
  - NMState
  - NuGet
  - NVidia
  - NVMe
  - OAuth
  - objectClass
  - OmniSharp
  - OneConnect
  - OpenEXR
  - OpenID
  - OpenIPMI
  - OpenJDK
  - OpenRAN
  - OpenRewrite
  - OpenSCAP
  - OpenShift
  - OpenSSH
  - OpenSSL
  - OpenStack
  - OpenTelemetry
  - OpenTracing
  - OperatorHub
  - OpEx
  - OSBuild
  - OTel
  - PaaS
  - PackageKit
  - PathTools
  - PCIe
  - PipeWire
  - PostgreSQL
  - PostScript
  - PowerPC
  - PowerShell
  - ProLiant
  - PulseAudio
  - PyPA
  - PyPI
  - QLogic
  - ReaR
  - RedBoot
  - relaxngDatatype
  - RESTEasy
  - RHEL
  - RoCE
  - SaaS
  - SeaBIOS
  - SELinux
  - SmallRye
  - SmartNIC
  - SmartState
  - SQLite
  - StarOffice
  - STMicroelectronics
  - SuperLU
  - SysV
  - TBps
  - TiB
  - TuneD
  - TypeScript
  - UltraSPARC
  - USBGuard
  - vCenter
  - vDisk
  - vHost
  - VMware
  - vSphere
  - vSwitch
  - WebAuthn
  - WebSocket
  - WireGuard
  - XEmacs
  - xPaaS
  - XString
  - XWayland
  - YouTube
  - ZCentral
