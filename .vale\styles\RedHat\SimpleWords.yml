---
extends: substitution
ignorecase: true
level: suggestion
link: https://redhat-documentation.github.io/vale-at-red-hat/docs/main/reference-guide/simplewords/
message: "Use simple language. Consider using '%s' rather than '%s'."
action:
  name: replace
swap:
  "approximate(?:ly)?": about
  "objective(?! C?)": aim|goal
  absent: none|not here
  abundance: plenty
  accentuate: stress
  accompany: go with
  accomplish: carry out|do
  accorded: given
  accordingly: so
  accrue: add
  accurate: right|exact
  acquiesce: agree
  acquire: get|buy
  addressees: you
  adjacent to: next to
  adjustment: change
  admissible: allowed
  advantageous: helpful
  advise: tell
  aggregate: total
  aircraft: plane
  alleviate: ease
  allocate: assign|divide
  alternatively: or
  alternatives: choices|options
  ameliorate: improve
  amend: change
  anticipate: expect
  apparent: clear|plain
  ascertain: discover|find out
  assistance: help
  attain: meet
  attempt: try
  authorize: allow
  belated: late
  bestow: give
  cease: stop|end
  collaborate: work together
  commence: begin
  compensate: pay
  component: part
  comprise: form|include
  concerning: about
  confer: give|award
  consequently: so
  consolidate: merge
  constitutes: forms
  contains: has
  convene: meet
  demonstrate: show|prove
  depart: leave
  designate: choose
  desire: want|wish
  determine: decide|find
  detrimental: bad|harmful
  disclose: share|tell
  discontinue: stop
  disseminate: send|give
  eliminate: end
  elucidate: explain
  employ: use
  enclosed: inside|included
  encounter: meet
  endeavor: try
  enumerate: count
  equitable: fair
  equivalent: equal
  exclusively: only
  expedite: hurry
  facilitate: ease
  females: women
  finalize: complete|finish
  frequently: often
  identical: same
  incorrect: wrong
  indication: sign
  initiate: start|begin
  itemized: listed
  jeopardize: risk
  liaise: work with|partner with
  maintain: keep|support
  methodology: method
  modify: change
  monitor: check|watch
  multiple: many
  necessitate: cause
  notify: tell
  numerous: many
  obligate: bind|compel
  optimum: best|most
  permit: let
  portion: part
  possess: own
  previous: earlier
  previously: before
  prioritize: rank
  procure: buy
  provide: give|offer
  purchase: buy
  relocate: move
  solicit: request
  state-of-the-art: latest
  subsequent: later|next
  substantial: large
  sufficient: enough
  terminate: end
  transmit: send
  utilization: use
  utilize: use
  utilizing: using
