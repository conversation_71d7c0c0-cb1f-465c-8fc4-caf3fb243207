#file: noinspection IncorrectFormatting
---
extends: spelling
level: warning
link: https://redhat-documentation.github.io/vale-at-red-hat/docs/main/reference-guide/spelling/
message: "Verify the word '%s'. It is not in the American English spelling dictionary used by Vale."
# A "filter" is a case-sensitive regular expression specifying words to ignore during spell checking.
# Spelling rule applies to individual words
# There is no need to add plural forms (s?) to the regex
filters:
  - "[aA]ccessor"
  - "[aA]llowlist"
  - "[aA]utogenerate"
  - "[aA]utoinstall"
  - "[aA]utomount"
  - "[aA]utonumber"
  - "[aA]utostart"
  - "[bB]ackfilling"
  - "[bB]ackport"
  - "[bB]ackported"
  - "[bB]acktrace"
  - "[bB]indable"
  - "[bB]oolean"
  - "[bB]ootable"
  - "[bB]reakpoint(s)?"
  - '\b[cC]he\b'
  - "[cC]hrony"
  - "[cC]lassloading"
  - "[cC]lasspath"
  - "[cC]olocate"
  - "[cC]olocation"
  - '\b[cC]onfig\b'
  - "[cC]ontainerd"
  - "[cC]orequisite"
  - "[cC]ustomizer"
  - "[cC]yberattack"
  - "[cC]ybercrime"
  - "[cC]ybersecurity"
  - "[dD]atasource"
  - "[dD]eclaratively"
  - "[dD]ecompiler"
  - "[dD]efragmentation"
  - "[dD]eserialization"
  - "[dD]eserialize(d)?"
  - "[dD]esynchronize(d)?"
  - '\b[dD]ev\b'
  - "[dD]ev[wW]orkspace"
  - "[dD]evfile"
  - "[dD]istro"
  - "[dD]ownstream"
  - "[dD]ownstreaming"
  - "[eE]xposal"
  - "[eE]xtrapartition"
  - "[Ff]actories"
  - "[Ff]actory"
  - "[fF]ailback"
  - "[fF]ailover"
  - "[fF]indability"
  - "[gG]bps"
  - "[gG]ibibyte"
  - '\b[gG]it\b'
  - "[gG]lock"
  - "[gG]radle"
  - "[gG]rafana"
  - "[hH]ardcoding"
  - "[hH]eatmap"
  - "[hH]ostname"
  - "[hH]yperconverged"
  - "[iI]node"
  - "[iI]ntranode"
  - "[iI]ntrapartition"
  - "[iI]ntrarecord"
  - "[iI]ntrasystem"
  - "[iI]nvocable"
  - '\b[iI]tem\b'
  - "[jJ]et[bB]rains"
  - "[jJ]ournald"
  - "[jJ]ournaling"
  - "[kK]dump"
  - "[kK]eycloak"
  - "[kK]eylime"
  - "[kK]eyring"
  - "[kK]eyrings"
  - "[kK]eyspace"
  - "[kK]eytab"
  - "[kK]ustomize"
  - "[lL]ibvirt"
  - "[lL]icensor"
  - "[lL]iquibase"
  - "[lL]iveness"
  - "[lL]oopback"
  - "[mM]acrostructure"
  - "[mM]atrixes"
  - "[mM]ebibytes"
  - "[mM]etaclass"
  - "[mM]etafile"
  - "[mM]etamodel"
  - "[mM]etatable"
  - "[mM]iddleware"
  - "[mM]illicores"
  - "[mM]inicourse"
  - "[mM]inidisk"
  - "[mM]ixin"
  - "[mM]ixins"
  - "[mM]odularization"
  - "[Mm]onospace"
  - "[mM]ulticloud"
  - "[mM]ulticluster"
  - "[mM]ultifactor"
  - "[mM]ultihost"
  - "[mM]ultinode"
  - "[mM]ultipath"
  - "[mM]ultipoint"
  - "[mM]ultisite"
  - "[mM]ultitable"
  - "[mM]ultitenancy"
  - "[mM]ultitenant"
  - "[mM]ultithread"
  - "[mM]ultitiered"
  - "[mM]ultiuser"
  - "[mM]ultivendor"
  - "[mM]ultizone"
  - "[nN]amespace"
  - "[nN]amespaces"
  - "[nN]MState"
  - "[nN]onheap"
  - "[nN]oninteractive"
  - "[nN]onpaired"
  - "[nN]onsystem"
  - "[oO]ffboarding"
  - "[oO]mnichannel"
  - "[oO]nboarding"
  - "[oO]perator"
  - "[oO]verridable"
  - "[oO]verstrike"
  - "[pP]luggable"
  - "[pP]ostediting"
  - "[Pp]ostinstall"
  - "[pP]ostinstallation"
  - "[pP]ostoperation"
  - "[pP]ostrequisite"
  - "[pP]recompile"
  - "[pP]reconfigure(d)?"
  - "[pP]reenrollment"
  - "[pP]reformatted"
  - "[pP]regenerated"
  - "[Pp]reinstall"
  - "[pP]reinstallation"
  - "[pP]reoperational"
  - "[pP]repend"
  - "[pP]repended"
  - "[pP]reprocess"
  - "[pP]reprocessor"
  - "[pP]roductize"
  - "[pP]roductized"
  - "[pP]seudocode"
  - "[pP]seudorandom"
  - "[pP]seudotext"
  - "[pP]ulldown"
  - "[rR]eadonly"
  - "[rR]eauthenticate"
  - "[rR]ebalance"
  - "[rR]ebalances"
  - "[rR]ebalancing"
  - "[rR]ebase"
  - "[rR]ebased"
  - "[rR]ecertification"
  - "[rR]ecertifications"
  - "[rR]eenabled"
  - "[rR]eentrant"
  - "[rR]efreshable"
  - "[rR]eindex"
  - "[rR]eindexing"
  - "[rR]eshard"
  - "[rR]esharding"
  - "[rR]eshards"
  - "[rR]esyncing"
  - "[rR]ollout"
  - "[rR]ollouts"
  - "[rR]oundtable"
  - "[rR]oundtables"
  - "[rR]ulebook"
  - "[rR]uleset"
  - "[rR]unlevel"
  - "[rR]unlevels"
  - "[rR]untime"
  - "[rR]untimes"
  - "[sS]crollbar"
  - "[sS]earchability"
  - "[sS]erializable"
  - "[sS]erialization"
  - "[sS]erializer"
  - "[sS]erverless"
  - "[sS]ervlet"
  - "[sS]etter"
  - "[sS]harding"
  - '\b[Ss]u\b'
  - "[sS]ubcommand(s)?"
  - "[sS]ubmenu(s)?"
  - "[sS]ubnet(s)?"
  - "[sS]ubnetwork(s)?"
  - "[sS]ubpackage(s)?"
  - "[sS]ubpath(s)?"
  - "[sS]ubstep(s)?"
  - "[sS]ubtest(s)?"
  - "[sS]ubuser(s)?"
  - "[sS]ubvolume(s)?"
  - "[sS]ysctl"
  - "[sS]yslog"
  - "[sS]ystemd"
  - "[tT]elco"
  - "[tT]emplated"
  - "[tT]heia"
  - "[tT]olerations"
  - "[tT]raceback"
  - "[tT]ruststore"
  - "[uU]mask"
  - "[uU]ncomment"
  - "[uU]ndercloud"
  - "[uU]nderrun"
  - "[uU]nformatted"
  - "[uU]ninstallation"
  - "[uU]nmount"
  - "[uU]nmounting"
  - "[uU]nported"
  - "[uU]nstaged"
  - "[uU]ntrusted"
  - "[uU]psell"
  - "[uU]pselling"
  - "[vV]alidator"
  - "I/O"
  - "Let\\'s Encrypt"
  - '\.NET'
  - 'Node\.js'
  - ACLs
  - adoc
  - AGPLv
  - Agroal
  - Annobin
  - Ansible
  - Antora
  - API
  - Applixware
  - Asciidoctor
  - AssertJ
  - autoconfigure
  - autolink
  - aws
  - AWS
  - Azure
  - bcrypt
  - Bierner
  - Bitbucket
  - BOM
  - Bonjour
  - Bouncy Castle
  - btn
  - Btrfs
  - Bugzilla
  - Buildah
  - camelCase
  - CentOS
  - Ceph
  - cephfs
  - cgroup
  - Che-Theia
  - Ciphertext
  - Civetweb
  - classloader
  - Cloudbursting
  - Cloudwashing
  - CodeReady
  - ConfigMap
  - Containerfile
  - Cookiecutter
  - CPUs
  - CR
  - CRD
  - CRDs
  - CRs
  - CSIDriver
  - CSINode
  - CSVs
  - Ctrl
  - CVEs
  - Cygmon
  - DaemonSet
  - Datadog
  - Dev
  - DevWorkspace
  - Dex
  - DNS
  - Docker
  - Dockerfile
  - Dockerfiles
  - Dotnet
  - Dyninst
  - Endevor
  - Endian
  - endif
  - eServer
  - Esprima
  - etcd
  - Exif
  - extranet
  - Fabrice
  - Facter
  - Failsafe
  - FDs
  - Fernflower
  - firewalld
  - Flathub
  - Flatpak
  - Fluentd
  - Flyway
  - Fortran
  - Funqy
  - Galera
  - GCC
  - GIDs
  - GitHub
  - GitLab
  - Gluster
  - GNUPro
  - GraalVM
  - GraphQL
  - Graylog
  - Grayscale
  - greenboot
  - gRPC
  - GTID
  - GUI
  - Hashicorp
  - Helgrind
  - Helm
  - Homebrew
  - htmltest
  - http
  - HTTP
  - https
  - HTTPS
  - IDE
  - IDEs
  - IKEv
  - Infinispan
  - Intelephense
  - IntelliJ
  - IPPool
  - IPsec
  - IPv
  - ISeries
  - Istio
  - ISVs
  - Itanium
  - Jakarta
  - Jandex
  - Java
  - Jave
  - JBang
  - JBoss
  - Jira
  - Jolokia
  - Joyent
  - JUnit
  - jvm
  - JVM
  - Kafka
  - kbd
  - Kerberos
  - keystore
  - Kibana
  - Knative
  - Knowledgebase
  - Kogito
  - Kompose
  - kubelet
  - Kubernetes
  - Kubespray
  - Kylin
  - Laravel
  - LGPLv
  - libOSMesa
  - librados
  - librbd
  - Libreswan
  - Liquibase
  - Logstash
  - Lombok
  - Makefile
  - Mattermost
  - Maven
  - MicroProfile
  - Microsoft
  - Minikube
  - Minishift
  - Mirantis
  - Mockito
  - MongoDB
  - multischema
  - MySQL
  - Nagios
  - Narayana
  - Neoverse
  - NetcoredebugOutput
  - Netty
  - Newdoc
  - NFSv
  - Nginx
  - npm
  - NuGet
  - Nutanix
  - NVidia
  - NVMe
  - OAuth
  - objectClass
  - ocp
  - OmniSharp
  - OpenID
  - OpenJDK
  - OpenRewrite
  - OpenShift
  - OpenTelemetry
  - OpenTracing
  - OSBuild
  - osd
  - OSs
  - OSTree
  - OTel
  - PCIe
  - PDF
  - Petitboot
  - PHP
  - PIDs
  - Podman
  - PostgreSQL
  - PowerShell
  - precache
  - Prometheus
  - proxied
  - PVCs
  - Pytorch
  - qdmanage
  - qdstat
  - qeth
  - QLogic
  - Quarkiverse
  - Quarkus
  - Quiltflower
  - Qute
  - Realtime
  - Redis
  - Redistributions
  - relaxngDatatype
  - RESTEasy
  - Restic
  - Rolfe
  - Rollup
  - ROMs
  - rootful
  - rootless
  - RPMs
  - Sakila
  - sbt
  - SCM
  - SELinux
  - Semeru
  - Shadowman
  - Skopeo
  - SLAs
  - SmallRye
  - Spotify
  - startx
  - STMicroelectronics
  - Stratis
  - subaddress
  - subcapacity
  - subtab
  - superobject
  - Suchow
  - SVG
  - Symfony
  - Tekton
  - Telekom
  - Temurin
  - Tensorflow
  - Texinfo
  - Toolset
  - Traefik
  - Uber
  - UIDs
  - URI
  - url
  - URL
  - USBGuard
  - Vale
  - Valgrind
  - vCenter
  - vDisk
  - Velero
  - Vert.x
  - vHost
  - VMs
  - VMware
  - vsix
  - vSphere
  - WebAuthn
  - Webpack
  - WebSocket
  - WebView
  - wifi
  - Wildfly
  - Woopra
  - Wordpress
  - XString
  - XWayland
  - Yana
  - Yeoman
  - ZCentral
  - Zowe
