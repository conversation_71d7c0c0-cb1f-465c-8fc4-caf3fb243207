{{- /* See https://github.com/errata-ai/vale/issues/614 */ -}}
{{- /* See https://vale.sh/manual/output/ */ -}}

{{- /* Keep track of our various counts */ -}}

{{- $e := 0 -}}
{{- $w := 0 -}}
{{- $s := 0 -}}
{{- $f := 0 -}}

{{- /* Range over the linted files */ -}}

{{- range .Files}}
{{$table := newTable true}}

{{- $f = add1 $f -}}
{{- .Path | underline | indent 1 -}}
{{- "\n" -}}

{{- $msgToLoc := dict -}}
{{- $msgToLvl := dict -}}
{{- $msgToChk := dict -}}

{{- /* Range over the file's alerts */ -}}

{{- range .Alerts -}}

{{- $error := "" -}}
{{- if eq .Severity "error" -}}
    {{- $error = .Severity | red -}}
    {{- $e = add1 $e  -}}
{{- else if eq .Severity "warning" -}}
    {{- $error = .Severity | yellow -}}
    {{- $w = add1 $w -}}
{{- else -}}
    {{- $error = .Severity | blue -}}
    {{- $s = add1 $s -}}
{{- end}}

{{- $loc := printf "%d:%d" .Line (index .Span 0) -}}

{{- $locations := get $msgToLoc .Message -}}

{{- $_ := set $msgToLoc .Message (cat $locations $loc) -}}
{{- $_ := set $msgToLvl .Message $error -}}
{{- $_ := set $msgToChk .Message .Check -}}

{{end -}}

{{- range keys $msgToLoc -}}

{{- $msg := . -}}
{{- $loc := trimPrefix "," ((splitList " " (get $msgToLoc $msg)) | join ",") -}}
{{- $lvl := get $msgToLvl $msg -}}
{{- $chk := get $msgToChk $msg -}}

{{- $row := list $loc $lvl $msg $chk | toStrings -}}
{{- $table = addRow $table $row -}}

{{end -}}

{{- $table = renderTable $table -}}

{{end}}

{{- $e}} {{"errors" | red}}, {{$w}} {{"warnings" | yellow}} and {{$s}} {{"suggestions" | blue}} in {{$f}} {{$f | int | plural "file" "files"}}.
