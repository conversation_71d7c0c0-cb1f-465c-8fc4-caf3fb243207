
# `llm-d-deployer`

This repository includes examples, Helm charts, and release assets for `llm-d`:

- Quickstarts for experimenting with `llm-d` features:
  - [Try with minikube](quickstart/README-minikube.md)
  - [Try on Kubernetes](quickstart/README.md)
- Helm charts for deploying `llm-d`
  - See [charts/llm-d/README.md](charts/llm-d/README.md)
- Release assets for running `llm-d`
  - See [release notes](https://github.com/llm-d/llm-d-deployer/releases)

## Contributing

See [CONTRIBUTING.md](CONTRIBUTING.md) for instructions on how to contribute to this repository.

## License

This project is licensed under the Apache License 2.0. See the [LICENSE](LICENSE) file for details.
