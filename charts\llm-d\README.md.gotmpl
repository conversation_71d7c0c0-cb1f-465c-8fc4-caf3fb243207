# llm-d Helm Chart

{{ template "chart.deprecationWarning" . }}

{{ template "chart.versionBadge" . }}
{{ template "chart.typeBadge" . }}

{{ template "chart.description" . }}

{{ template "chart.homepageLine" . }}

{{ template "chart.maintainersSection" . }}

{{ template "chart.sourcesSection" . }}

---

## TL;DR

```console
helm repo add bitnami https://charts.bitnami.com/bitnami
helm repo add llm-d https://llm-d.ai/llm-d-deployer

helm install my-llm-d llm-d/llm-d
```

## Prerequisites

- Git (v2.25 or [latest](https://github.com/git-guides/install-git#install-git-on-linux), for sparse-checkout support)
- Kubectl (1.25+ or [latest](https://kubernetes.io/docs/tasks/tools/install-kubectl-linux/) with built-in kustomize support)

```shell
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
chmod +x kubectl
sudo mv kubectl /usr/local/bin/
```

- Kubernetes 1.30+ (OpenShift 4.17+)
- Helm 3.10+ or [latest release](https://github.com/helm/helm/releases)
- [Gateway API](https://gateway-api.sigs.k8s.io/guides/) (see for [examples](https://github.com/llm-d/llm-d-deployer/blob/6db03770626f6e67b099300c66bfa535b2504727/chart-dependencies/ci-deps.sh#L22) we use in our CI)
- [kGateway](https://kgateway.dev/) (or [Istio](http://istio.io/)) installed in the cluster (see for [examples](https://github.com/llm-d/llm-d-deployer/blob/6db03770626f6e67b099300c66bfa535b2504727/chart-dependencies/kgateway/install.sh) we use in our CI)

## Usage

Charts are available in the following formats:

- [Chart Repository](https://helm.sh/docs/topics/chart_repository/)
- [OCI Artifacts](https://helm.sh/docs/topics/registries/)

### Installing from the Chart Repository

The following command can be used to add the chart repository:

```console
helm repo add bitnami https://charts.bitnami.com/bitnami
helm repo add llm-d https://llm-d.ai/llm-d-deployer
```

Once the chart has been added, install this chart. However before doing so, please review the default `values.yaml` and adjust as needed.


```console
helm upgrade -i <release_name> llm-d/llm-d
```

### Installing from an OCI Registry

Charts are also available in OCI format. The list of available releases can be found [here](https://github.com/orgs/llm-d/packages/container/package/llm-d-deployer%2Fllm-d).

Install one of the available versions:

```shell
helm upgrade -i <release_name> oci://ghcr.io/llm-d/llm-d-deployer/llm-d --version=<version>
```

> **Tip**: List all releases using `helm list`

### Testing a Release

Once an Helm Release has been deployed, you can test it using the [`helm test`](https://helm.sh/docs/helm/helm_test/) command:

```sh
helm test <release_name>
```

This will run a simple Pod in the cluster to check that the application deployed is up and running.

You can control whether to disable this test pod or you can also customize the image it leverages.
See the `test.enabled` and `test.image` parameters in the [`values.yaml`](./values.yaml) file.

> **Tip**: Disabling the test pod will not prevent the `helm test` command from passing later on. It will simply report that no test suite is available.

Below are a few examples:

<details>

<summary>Disabling the test pod</summary>

```sh
helm install <release_name> <repo_or_oci_registry> \
  --set test.enabled=false
```

</details>

<details>

<summary>Customizing the test pod image</summary>

```sh
helm install <release_name> <repo_or_oci_registry> \
  --set test.image.repository=curl/curl-base \
  --set test.image.tag=8.11.1
```

</details>

### Uninstalling the Chart

To uninstall/delete the `my-llm-d-release` deployment:

```console
helm uninstall my-llm-d-release
```

The command removes all the Kubernetes components associated with the chart and deletes the release.

{{ template "chart.requirementsSection" . }}

{{ template "chart.valuesSection" . }}


## Features

This chart deploys all infrastructure required to run the [llm-d](https://llm-d.ai/) project. It includes:

- A Gateway
- A `ModelService` CRD
- A [Model Service controller](https://github.com/llm-d/llm-d-model-service) with full RBAC support
- [Redis](https://github.com/bitnami/charts/tree/main/bitnami/redis) deployment for LMCache and smart routing
- Enabled monitoring and metrics scraping for llm-d components

Once deployed you can create `ModelService` CRs to deploy your models. The model service controller will take care of deploying the models and exposing them through the Gateway.

### Sample Application

By default the chart also deploys a sample application that deploys a Llama model. See `.Values.sampleApplication` in the `values.yaml` file for more details. If you wish to get rid of it, set `sampleApplication.enabled` to `false` in the `values.yaml` file:

```bash
helm upgrade -i <release_name> llm-d/llm-d \
  --set sampleApplication.enabled=false
```

### Metrics collection

There are various metrics exposed by the llm-d components. To enable/disable scraping of these metrics, look for `metrics.enabled` toggles in the `values.yaml` file. By default, all components have metrics enabled.

### Model Service

A new custom resource definition (CRD) called `ModelService` is created by the chart. This CRD is used to deploy models on the cluster. The model service controller will take care of deploying the models.

To see the full spec of the `ModelService` CRD, refer to the [ModelService CRD API reference](https://github.com/llm-d/llm-d-model-service/blob/main/docs/api_reference/out.asciidoc).

A basic example of a `ModelService` CR looks like this:

```yaml
apiVersion: llm-d.ai/v1alpha1
kind: ModelService
metadata:
  name: <name>
spec:
  decoupleScaling: false
  baseConfigMapRef:
    name: basic-gpu-with-nixl-and-redis-lookup-preset
  routing:
    modelName: <model_name>
  modelArtifacts:
    uri: pvc://<pvc_name>/<path_to_model>
  decode:
    replicas: 1
    containers:
    - name: "vllm"
      args:
      - "--model"
      - <model_name>
  prefill:
    replicas: 1
    containers:
    - name: "vllm"
      args:
      - "--model"
      - <model_name>
```

## Quickstart

If you want to get started quickly and experiment with llm-d, you can also take a look at the [Quickstart](https://github.com/llm-d/llm-d-deployer/blob/main/quickstart/README.md) we provide. It wraps this chart and deploys a full llm-d stack with all it's prerequisites a sample application.

## Contributing

We welcome contributions to this chart! If you have any suggestions or improvements, please feel free to open an issue or submit a pull request. Please read our [contributing guide](https://github.com/llm-d/llm-d-deployer/blob/main/CONTRIBUTING.md) on how to submit a pull request.
