apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.17.2
  name: modelservices.llm-d.ai
spec:
  group: llm-d.ai
  names:
    kind: ModelService
    listKind: ModelServiceList
    plural: modelservices
    shortNames:
    - msvc
    singular: modelservice
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .spec.decoupleScaling
      name: Decouple Scaling
      type: boolean
    - jsonPath: .status.prefillReady
      name: Prefill READY
      type: string
    - jsonPath: .status.prefillAvailable
      name: Prefill AVAIL
      type: integer
    - jsonPath: .status.decodeReady
      name: Decode READY
      type: string
    - jsonPath: .status.decodeAvailable
      name: Decode AVAIL
      type: integer
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: ModelService is the Schema for the modelservices API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: ModelServiceSpec defines the desired state of ModelService
            properties:
              baseConfigMapRef:
                description: BaseConfigMapRef provides configuration needed to spawn
                  objects owned by modelservice
                properties:
                  apiVersion:
                    description: API version of the referent.
                    type: string
                  fieldPath:
                    description: |-
                      If referring to a piece of an object instead of an entire object, this string
                      should contain a valid JSON/Go field access statement, such as desiredState.manifest.containers[2].
                      For example, if the object reference is to a container within a pod, this would take on a value like:
                      "spec.containers{name}" (where "name" refers to the name of the container that triggered
                      the event) or if no container name is specified "spec.containers[2]" (container with
                      index 2 in this pod). This syntax is chosen only to have some well-defined way of
                      referencing a part of an object.
                    type: string
                  kind:
                    description: |-
                      Kind of the referent.
                      More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
                    type: string
                  name:
                    description: |-
                      Name of the referent.
                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                    type: string
                  namespace:
                    description: |-
                      Namespace of the referent.
                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/namespaces/
                    type: string
                  resourceVersion:
                    description: |-
                      Specific resourceVersion to which this reference is made, if any.
                      More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#concurrency-control-and-consistency
                    type: string
                  uid:
                    description: |-
                      UID of the referent.
                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#uids
                    type: string
                type: object
                x-kubernetes-map-type: atomic
              decode:
                description: Decode is the decode portion of the spec
                properties:
                  acceleratorTypes:
                    description: |-
                      pod
                      AcceleratorTypes determines the set of accelerators on which
                      this pod will be run. Any matching accelerator type can be used
                      to place the model pods.This will override base config when present
                    properties:
                      labelKey:
                        description: |-
                          node label key that identifies the accelerator type for the node
                          e.g., nvidia.com/gpu.product
                        type: string
                      labelValues:
                        description: |-
                          node label values that will be matched against for pod scheduling.
                          e.g., [A100, H100]
                        items:
                          type: string
                        minItems: 1
                        type: array
                    required:
                    - labelKey
                    - labelValues
                    type: object
                  containers:
                    description: Container holds vllm container container details
                      that will be overridden from base config when present.
                    items:
                      description: ContainerSpec defines container-level configuration.
                      properties:
                        args:
                          description: |-
                            Arguments to the entrypoint.
                            The container image's CMD is used if this is not provided.
                            Variable references $(VAR_NAME) are expanded using the container's environment. If a variable
                            cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced
                            to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. "$$(VAR_NAME)" will
                            produce the string literal "$(VAR_NAME)". Escaped references will never be expanded, regardless
                            of whether the variable exists or not. Cannot be updated.
                            More info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        command:
                          description: |-
                            Entrypoint array. Not executed within a shell.
                            The container image's ENTRYPOINT is used if this is not provided.
                            Variable references $(VAR_NAME) are expanded using the container's environment. If a variable
                            cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced
                            to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. "$$(VAR_NAME)" will
                            produce the string literal "$(VAR_NAME)". Escaped references will never be expanded, regardless
                            of whether the variable exists or not. Cannot be updated.
                            More info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        env:
                          description: |-
                            List of environment variables to set in the container.
                            Cannot be updated.
                          items:
                            description: EnvVar represents an environment variable
                              present in a Container.
                            properties:
                              name:
                                description: Name of the environment variable. Must
                                  be a C_IDENTIFIER.
                                type: string
                              value:
                                description: |-
                                  Variable references $(VAR_NAME) are expanded
                                  using the previously defined environment variables in the container and
                                  any service environment variables. If a variable cannot be resolved,
                                  the reference in the input string will be unchanged. Double $$ are reduced
                                  to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e.
                                  "$$(VAR_NAME)" will produce the string literal "$(VAR_NAME)".
                                  Escaped references will never be expanded, regardless of whether the variable
                                  exists or not.
                                  Defaults to "".
                                type: string
                              valueFrom:
                                description: Source for the environment variable's
                                  value. Cannot be used if value is not empty.
                                properties:
                                  configMapKeyRef:
                                    description: Selects a key of a ConfigMap.
                                    properties:
                                      key:
                                        description: The key to select.
                                        type: string
                                      name:
                                        default: ""
                                        description: |-
                                          Name of the referent.
                                          This field is effectively required, but due to backwards compatibility is
                                          allowed to be empty. Instances of this type with an empty value here are
                                          almost certainly wrong.
                                          More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                        type: string
                                      optional:
                                        description: Specify whether the ConfigMap
                                          or its key must be defined
                                        type: boolean
                                    required:
                                    - key
                                    type: object
                                    x-kubernetes-map-type: atomic
                                  fieldRef:
                                    description: |-
                                      Selects a field of the pod: supports metadata.name, metadata.namespace, `metadata.labels['<KEY>']`, `metadata.annotations['<KEY>']`,
                                      spec.nodeName, spec.serviceAccountName, status.hostIP, status.podIP, status.podIPs.
                                    properties:
                                      apiVersion:
                                        description: Version of the schema the FieldPath
                                          is written in terms of, defaults to "v1".
                                        type: string
                                      fieldPath:
                                        description: Path of the field to select in
                                          the specified API version.
                                        type: string
                                    required:
                                    - fieldPath
                                    type: object
                                    x-kubernetes-map-type: atomic
                                  resourceFieldRef:
                                    description: |-
                                      Selects a resource of the container: only resources limits and requests
                                      (limits.cpu, limits.memory, limits.ephemeral-storage, requests.cpu, requests.memory and requests.ephemeral-storage) are currently supported.
                                    properties:
                                      containerName:
                                        description: 'Container name: required for
                                          volumes, optional for env vars'
                                        type: string
                                      divisor:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        description: Specifies the output format of
                                          the exposed resources, defaults to "1"
                                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                        x-kubernetes-int-or-string: true
                                      resource:
                                        description: 'Required: resource to select'
                                        type: string
                                    required:
                                    - resource
                                    type: object
                                    x-kubernetes-map-type: atomic
                                  secretKeyRef:
                                    description: Selects a key of a secret in the
                                      pod's namespace
                                    properties:
                                      key:
                                        description: The key of the secret to select
                                          from.  Must be a valid secret key.
                                        type: string
                                      name:
                                        default: ""
                                        description: |-
                                          Name of the referent.
                                          This field is effectively required, but due to backwards compatibility is
                                          allowed to be empty. Instances of this type with an empty value here are
                                          almost certainly wrong.
                                          More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                        type: string
                                      optional:
                                        description: Specify whether the Secret or
                                          its key must be defined
                                        type: boolean
                                    required:
                                    - key
                                    type: object
                                    x-kubernetes-map-type: atomic
                                type: object
                            required:
                            - name
                            type: object
                          type: array
                          x-kubernetes-list-map-keys:
                          - name
                          x-kubernetes-list-type: map
                        envFrom:
                          description: |-
                            List of sources to populate environment variables in the container.
                            The keys defined within a source must be a C_IDENTIFIER. All invalid keys
                            will be reported as an event when the container is starting. When a key exists in multiple
                            sources, the value associated with the last source will take precedence.
                            Values defined by an Env with a duplicate key will take precedence.
                            Cannot be updated.
                          items:
                            description: EnvFromSource represents the source of a
                              set of ConfigMaps or Secrets
                            properties:
                              configMapRef:
                                description: The ConfigMap to select from
                                properties:
                                  name:
                                    default: ""
                                    description: |-
                                      Name of the referent.
                                      This field is effectively required, but due to backwards compatibility is
                                      allowed to be empty. Instances of this type with an empty value here are
                                      almost certainly wrong.
                                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                    type: string
                                  optional:
                                    description: Specify whether the ConfigMap must
                                      be defined
                                    type: boolean
                                type: object
                                x-kubernetes-map-type: atomic
                              prefix:
                                description: Optional text to prepend to the name
                                  of each environment variable. Must be a C_IDENTIFIER.
                                type: string
                              secretRef:
                                description: The Secret to select from
                                properties:
                                  name:
                                    default: ""
                                    description: |-
                                      Name of the referent.
                                      This field is effectively required, but due to backwards compatibility is
                                      allowed to be empty. Instances of this type with an empty value here are
                                      almost certainly wrong.
                                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                    type: string
                                  optional:
                                    description: Specify whether the Secret must be
                                      defined
                                    type: boolean
                                type: object
                                x-kubernetes-map-type: atomic
                            type: object
                          type: array
                          x-kubernetes-list-type: atomic
                        image:
                          description: Image that is used to spawn container if present
                            will override base config
                          type: string
                        name:
                          description: |-
                            Name of the container specified as a DNS_LABEL.
                            Each container in a pod must have a unique name (DNS_LABEL).
                            Cannot be updated.
                          type: string
                        resources:
                          description: |-
                            Compute Resources required by this container.
                            Cannot be updated.
                            More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                          properties:
                            claims:
                              description: |-
                                Claims lists the names of resources, defined in spec.resourceClaims,
                                that are used by this container.

                                This is an alpha field and requires enabling the
                                DynamicResourceAllocation feature gate.

                                This field is immutable. It can only be set for containers.
                              items:
                                description: ResourceClaim references one entry in
                                  PodSpec.ResourceClaims.
                                properties:
                                  name:
                                    description: |-
                                      Name must match the name of one entry in pod.spec.resourceClaims of
                                      the Pod where this field is used. It makes that resource available
                                      inside a container.
                                    type: string
                                  request:
                                    description: |-
                                      Request is the name chosen for a request in the referenced claim.
                                      If empty, everything from the claim is made available, otherwise
                                      only the result of this request.
                                    type: string
                                required:
                                - name
                                type: object
                              type: array
                              x-kubernetes-list-map-keys:
                              - name
                              x-kubernetes-list-type: map
                            limits:
                              additionalProperties:
                                anyOf:
                                - type: integer
                                - type: string
                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                x-kubernetes-int-or-string: true
                              description: |-
                                Limits describes the maximum amount of compute resources allowed.
                                More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                              type: object
                            requests:
                              additionalProperties:
                                anyOf:
                                - type: integer
                                - type: string
                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                x-kubernetes-int-or-string: true
                              description: |-
                                Requests describes the minimum amount of compute resources required.
                                If Requests is omitted for a container, it defaults to Limits if that is explicitly specified,
                                otherwise to an implementation-defined value. Requests cannot exceed Limits.
                                More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                              type: object
                          type: object
                      required:
                      - name
                      type: object
                    type: array
                  initContainers:
                    description: InitContainers holds vllm init container details
                      that will be overridden from base config when present.
                    items:
                      description: ContainerSpec defines container-level configuration.
                      properties:
                        args:
                          description: |-
                            Arguments to the entrypoint.
                            The container image's CMD is used if this is not provided.
                            Variable references $(VAR_NAME) are expanded using the container's environment. If a variable
                            cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced
                            to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. "$$(VAR_NAME)" will
                            produce the string literal "$(VAR_NAME)". Escaped references will never be expanded, regardless
                            of whether the variable exists or not. Cannot be updated.
                            More info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        command:
                          description: |-
                            Entrypoint array. Not executed within a shell.
                            The container image's ENTRYPOINT is used if this is not provided.
                            Variable references $(VAR_NAME) are expanded using the container's environment. If a variable
                            cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced
                            to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. "$$(VAR_NAME)" will
                            produce the string literal "$(VAR_NAME)". Escaped references will never be expanded, regardless
                            of whether the variable exists or not. Cannot be updated.
                            More info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        env:
                          description: |-
                            List of environment variables to set in the container.
                            Cannot be updated.
                          items:
                            description: EnvVar represents an environment variable
                              present in a Container.
                            properties:
                              name:
                                description: Name of the environment variable. Must
                                  be a C_IDENTIFIER.
                                type: string
                              value:
                                description: |-
                                  Variable references $(VAR_NAME) are expanded
                                  using the previously defined environment variables in the container and
                                  any service environment variables. If a variable cannot be resolved,
                                  the reference in the input string will be unchanged. Double $$ are reduced
                                  to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e.
                                  "$$(VAR_NAME)" will produce the string literal "$(VAR_NAME)".
                                  Escaped references will never be expanded, regardless of whether the variable
                                  exists or not.
                                  Defaults to "".
                                type: string
                              valueFrom:
                                description: Source for the environment variable's
                                  value. Cannot be used if value is not empty.
                                properties:
                                  configMapKeyRef:
                                    description: Selects a key of a ConfigMap.
                                    properties:
                                      key:
                                        description: The key to select.
                                        type: string
                                      name:
                                        default: ""
                                        description: |-
                                          Name of the referent.
                                          This field is effectively required, but due to backwards compatibility is
                                          allowed to be empty. Instances of this type with an empty value here are
                                          almost certainly wrong.
                                          More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                        type: string
                                      optional:
                                        description: Specify whether the ConfigMap
                                          or its key must be defined
                                        type: boolean
                                    required:
                                    - key
                                    type: object
                                    x-kubernetes-map-type: atomic
                                  fieldRef:
                                    description: |-
                                      Selects a field of the pod: supports metadata.name, metadata.namespace, `metadata.labels['<KEY>']`, `metadata.annotations['<KEY>']`,
                                      spec.nodeName, spec.serviceAccountName, status.hostIP, status.podIP, status.podIPs.
                                    properties:
                                      apiVersion:
                                        description: Version of the schema the FieldPath
                                          is written in terms of, defaults to "v1".
                                        type: string
                                      fieldPath:
                                        description: Path of the field to select in
                                          the specified API version.
                                        type: string
                                    required:
                                    - fieldPath
                                    type: object
                                    x-kubernetes-map-type: atomic
                                  resourceFieldRef:
                                    description: |-
                                      Selects a resource of the container: only resources limits and requests
                                      (limits.cpu, limits.memory, limits.ephemeral-storage, requests.cpu, requests.memory and requests.ephemeral-storage) are currently supported.
                                    properties:
                                      containerName:
                                        description: 'Container name: required for
                                          volumes, optional for env vars'
                                        type: string
                                      divisor:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        description: Specifies the output format of
                                          the exposed resources, defaults to "1"
                                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                        x-kubernetes-int-or-string: true
                                      resource:
                                        description: 'Required: resource to select'
                                        type: string
                                    required:
                                    - resource
                                    type: object
                                    x-kubernetes-map-type: atomic
                                  secretKeyRef:
                                    description: Selects a key of a secret in the
                                      pod's namespace
                                    properties:
                                      key:
                                        description: The key of the secret to select
                                          from.  Must be a valid secret key.
                                        type: string
                                      name:
                                        default: ""
                                        description: |-
                                          Name of the referent.
                                          This field is effectively required, but due to backwards compatibility is
                                          allowed to be empty. Instances of this type with an empty value here are
                                          almost certainly wrong.
                                          More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                        type: string
                                      optional:
                                        description: Specify whether the Secret or
                                          its key must be defined
                                        type: boolean
                                    required:
                                    - key
                                    type: object
                                    x-kubernetes-map-type: atomic
                                type: object
                            required:
                            - name
                            type: object
                          type: array
                          x-kubernetes-list-map-keys:
                          - name
                          x-kubernetes-list-type: map
                        envFrom:
                          description: |-
                            List of sources to populate environment variables in the container.
                            The keys defined within a source must be a C_IDENTIFIER. All invalid keys
                            will be reported as an event when the container is starting. When a key exists in multiple
                            sources, the value associated with the last source will take precedence.
                            Values defined by an Env with a duplicate key will take precedence.
                            Cannot be updated.
                          items:
                            description: EnvFromSource represents the source of a
                              set of ConfigMaps or Secrets
                            properties:
                              configMapRef:
                                description: The ConfigMap to select from
                                properties:
                                  name:
                                    default: ""
                                    description: |-
                                      Name of the referent.
                                      This field is effectively required, but due to backwards compatibility is
                                      allowed to be empty. Instances of this type with an empty value here are
                                      almost certainly wrong.
                                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                    type: string
                                  optional:
                                    description: Specify whether the ConfigMap must
                                      be defined
                                    type: boolean
                                type: object
                                x-kubernetes-map-type: atomic
                              prefix:
                                description: Optional text to prepend to the name
                                  of each environment variable. Must be a C_IDENTIFIER.
                                type: string
                              secretRef:
                                description: The Secret to select from
                                properties:
                                  name:
                                    default: ""
                                    description: |-
                                      Name of the referent.
                                      This field is effectively required, but due to backwards compatibility is
                                      allowed to be empty. Instances of this type with an empty value here are
                                      almost certainly wrong.
                                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                    type: string
                                  optional:
                                    description: Specify whether the Secret must be
                                      defined
                                    type: boolean
                                type: object
                                x-kubernetes-map-type: atomic
                            type: object
                          type: array
                          x-kubernetes-list-type: atomic
                        image:
                          description: Image that is used to spawn container if present
                            will override base config
                          type: string
                        name:
                          description: |-
                            Name of the container specified as a DNS_LABEL.
                            Each container in a pod must have a unique name (DNS_LABEL).
                            Cannot be updated.
                          type: string
                        resources:
                          description: |-
                            Compute Resources required by this container.
                            Cannot be updated.
                            More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                          properties:
                            claims:
                              description: |-
                                Claims lists the names of resources, defined in spec.resourceClaims,
                                that are used by this container.

                                This is an alpha field and requires enabling the
                                DynamicResourceAllocation feature gate.

                                This field is immutable. It can only be set for containers.
                              items:
                                description: ResourceClaim references one entry in
                                  PodSpec.ResourceClaims.
                                properties:
                                  name:
                                    description: |-
                                      Name must match the name of one entry in pod.spec.resourceClaims of
                                      the Pod where this field is used. It makes that resource available
                                      inside a container.
                                    type: string
                                  request:
                                    description: |-
                                      Request is the name chosen for a request in the referenced claim.
                                      If empty, everything from the claim is made available, otherwise
                                      only the result of this request.
                                    type: string
                                required:
                                - name
                                type: object
                              type: array
                              x-kubernetes-list-map-keys:
                              - name
                              x-kubernetes-list-type: map
                            limits:
                              additionalProperties:
                                anyOf:
                                - type: integer
                                - type: string
                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                x-kubernetes-int-or-string: true
                              description: |-
                                Limits describes the maximum amount of compute resources allowed.
                                More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                              type: object
                            requests:
                              additionalProperties:
                                anyOf:
                                - type: integer
                                - type: string
                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                x-kubernetes-int-or-string: true
                              description: |-
                                Requests describes the minimum amount of compute resources required.
                                If Requests is omitted for a container, it defaults to Limits if that is explicitly specified,
                                otherwise to an implementation-defined value. Requests cannot exceed Limits.
                                More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                              type: object
                          type: object
                      required:
                      - name
                      type: object
                    type: array
                  parallelism:
                    description: |-
                      vllm
                      Parallelism specifies vllm parallelism that will be overridden from base config when present.
                    properties:
                      tensor:
                        default: 1
                        description: |-
                          TensorParallelism corresponds to the same argument in vllm
                          This also corresponds to number of GPUs
                        format: int32
                        minimum: 0
                        nullable: true
                        type: integer
                    type: object
                  replicas:
                    default: 1
                    description: Replicas defines the desired number of replicas for
                      this deployment.
                    format: int32
                    minimum: 0
                    nullable: true
                    type: integer
                type: object
              decoupleScaling:
                description: |-
                  DecoupleScaling determines who owns the replica fields is the deployment objects
                  Set this to true if the intent is to autoscale with HPA, other autoscalers
                  Setting this to false will force the controller to manage deployment replicas based on
                  replica fields in this model service
                type: boolean
              endpointPicker:
                description: EndpointPicker is the endpoint picker (epp) portion of
                  the spec
                properties:
                  containers:
                    description: Container holds vllm container container details
                      that will be overridden from base config when present.
                    items:
                      description: ContainerSpec defines container-level configuration.
                      properties:
                        args:
                          description: |-
                            Arguments to the entrypoint.
                            The container image's CMD is used if this is not provided.
                            Variable references $(VAR_NAME) are expanded using the container's environment. If a variable
                            cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced
                            to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. "$$(VAR_NAME)" will
                            produce the string literal "$(VAR_NAME)". Escaped references will never be expanded, regardless
                            of whether the variable exists or not. Cannot be updated.
                            More info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        command:
                          description: |-
                            Entrypoint array. Not executed within a shell.
                            The container image's ENTRYPOINT is used if this is not provided.
                            Variable references $(VAR_NAME) are expanded using the container's environment. If a variable
                            cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced
                            to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. "$$(VAR_NAME)" will
                            produce the string literal "$(VAR_NAME)". Escaped references will never be expanded, regardless
                            of whether the variable exists or not. Cannot be updated.
                            More info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        env:
                          description: |-
                            List of environment variables to set in the container.
                            Cannot be updated.
                          items:
                            description: EnvVar represents an environment variable
                              present in a Container.
                            properties:
                              name:
                                description: Name of the environment variable. Must
                                  be a C_IDENTIFIER.
                                type: string
                              value:
                                description: |-
                                  Variable references $(VAR_NAME) are expanded
                                  using the previously defined environment variables in the container and
                                  any service environment variables. If a variable cannot be resolved,
                                  the reference in the input string will be unchanged. Double $$ are reduced
                                  to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e.
                                  "$$(VAR_NAME)" will produce the string literal "$(VAR_NAME)".
                                  Escaped references will never be expanded, regardless of whether the variable
                                  exists or not.
                                  Defaults to "".
                                type: string
                              valueFrom:
                                description: Source for the environment variable's
                                  value. Cannot be used if value is not empty.
                                properties:
                                  configMapKeyRef:
                                    description: Selects a key of a ConfigMap.
                                    properties:
                                      key:
                                        description: The key to select.
                                        type: string
                                      name:
                                        default: ""
                                        description: |-
                                          Name of the referent.
                                          This field is effectively required, but due to backwards compatibility is
                                          allowed to be empty. Instances of this type with an empty value here are
                                          almost certainly wrong.
                                          More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                        type: string
                                      optional:
                                        description: Specify whether the ConfigMap
                                          or its key must be defined
                                        type: boolean
                                    required:
                                    - key
                                    type: object
                                    x-kubernetes-map-type: atomic
                                  fieldRef:
                                    description: |-
                                      Selects a field of the pod: supports metadata.name, metadata.namespace, `metadata.labels['<KEY>']`, `metadata.annotations['<KEY>']`,
                                      spec.nodeName, spec.serviceAccountName, status.hostIP, status.podIP, status.podIPs.
                                    properties:
                                      apiVersion:
                                        description: Version of the schema the FieldPath
                                          is written in terms of, defaults to "v1".
                                        type: string
                                      fieldPath:
                                        description: Path of the field to select in
                                          the specified API version.
                                        type: string
                                    required:
                                    - fieldPath
                                    type: object
                                    x-kubernetes-map-type: atomic
                                  resourceFieldRef:
                                    description: |-
                                      Selects a resource of the container: only resources limits and requests
                                      (limits.cpu, limits.memory, limits.ephemeral-storage, requests.cpu, requests.memory and requests.ephemeral-storage) are currently supported.
                                    properties:
                                      containerName:
                                        description: 'Container name: required for
                                          volumes, optional for env vars'
                                        type: string
                                      divisor:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        description: Specifies the output format of
                                          the exposed resources, defaults to "1"
                                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                        x-kubernetes-int-or-string: true
                                      resource:
                                        description: 'Required: resource to select'
                                        type: string
                                    required:
                                    - resource
                                    type: object
                                    x-kubernetes-map-type: atomic
                                  secretKeyRef:
                                    description: Selects a key of a secret in the
                                      pod's namespace
                                    properties:
                                      key:
                                        description: The key of the secret to select
                                          from.  Must be a valid secret key.
                                        type: string
                                      name:
                                        default: ""
                                        description: |-
                                          Name of the referent.
                                          This field is effectively required, but due to backwards compatibility is
                                          allowed to be empty. Instances of this type with an empty value here are
                                          almost certainly wrong.
                                          More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                        type: string
                                      optional:
                                        description: Specify whether the Secret or
                                          its key must be defined
                                        type: boolean
                                    required:
                                    - key
                                    type: object
                                    x-kubernetes-map-type: atomic
                                type: object
                            required:
                            - name
                            type: object
                          type: array
                          x-kubernetes-list-map-keys:
                          - name
                          x-kubernetes-list-type: map
                        envFrom:
                          description: |-
                            List of sources to populate environment variables in the container.
                            The keys defined within a source must be a C_IDENTIFIER. All invalid keys
                            will be reported as an event when the container is starting. When a key exists in multiple
                            sources, the value associated with the last source will take precedence.
                            Values defined by an Env with a duplicate key will take precedence.
                            Cannot be updated.
                          items:
                            description: EnvFromSource represents the source of a
                              set of ConfigMaps or Secrets
                            properties:
                              configMapRef:
                                description: The ConfigMap to select from
                                properties:
                                  name:
                                    default: ""
                                    description: |-
                                      Name of the referent.
                                      This field is effectively required, but due to backwards compatibility is
                                      allowed to be empty. Instances of this type with an empty value here are
                                      almost certainly wrong.
                                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                    type: string
                                  optional:
                                    description: Specify whether the ConfigMap must
                                      be defined
                                    type: boolean
                                type: object
                                x-kubernetes-map-type: atomic
                              prefix:
                                description: Optional text to prepend to the name
                                  of each environment variable. Must be a C_IDENTIFIER.
                                type: string
                              secretRef:
                                description: The Secret to select from
                                properties:
                                  name:
                                    default: ""
                                    description: |-
                                      Name of the referent.
                                      This field is effectively required, but due to backwards compatibility is
                                      allowed to be empty. Instances of this type with an empty value here are
                                      almost certainly wrong.
                                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                    type: string
                                  optional:
                                    description: Specify whether the Secret must be
                                      defined
                                    type: boolean
                                type: object
                                x-kubernetes-map-type: atomic
                            type: object
                          type: array
                          x-kubernetes-list-type: atomic
                        image:
                          description: Image that is used to spawn container if present
                            will override base config
                          type: string
                        name:
                          description: |-
                            Name of the container specified as a DNS_LABEL.
                            Each container in a pod must have a unique name (DNS_LABEL).
                            Cannot be updated.
                          type: string
                        resources:
                          description: |-
                            Compute Resources required by this container.
                            Cannot be updated.
                            More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                          properties:
                            claims:
                              description: |-
                                Claims lists the names of resources, defined in spec.resourceClaims,
                                that are used by this container.

                                This is an alpha field and requires enabling the
                                DynamicResourceAllocation feature gate.

                                This field is immutable. It can only be set for containers.
                              items:
                                description: ResourceClaim references one entry in
                                  PodSpec.ResourceClaims.
                                properties:
                                  name:
                                    description: |-
                                      Name must match the name of one entry in pod.spec.resourceClaims of
                                      the Pod where this field is used. It makes that resource available
                                      inside a container.
                                    type: string
                                  request:
                                    description: |-
                                      Request is the name chosen for a request in the referenced claim.
                                      If empty, everything from the claim is made available, otherwise
                                      only the result of this request.
                                    type: string
                                required:
                                - name
                                type: object
                              type: array
                              x-kubernetes-list-map-keys:
                              - name
                              x-kubernetes-list-type: map
                            limits:
                              additionalProperties:
                                anyOf:
                                - type: integer
                                - type: string
                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                x-kubernetes-int-or-string: true
                              description: |-
                                Limits describes the maximum amount of compute resources allowed.
                                More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                              type: object
                            requests:
                              additionalProperties:
                                anyOf:
                                - type: integer
                                - type: string
                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                x-kubernetes-int-or-string: true
                              description: |-
                                Requests describes the minimum amount of compute resources required.
                                If Requests is omitted for a container, it defaults to Limits if that is explicitly specified,
                                otherwise to an implementation-defined value. Requests cannot exceed Limits.
                                More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                              type: object
                          type: object
                      required:
                      - name
                      type: object
                    type: array
                  initContainers:
                    description: InitContainers holds vllm init container details
                      that will be overridden from base config when present.
                    items:
                      description: ContainerSpec defines container-level configuration.
                      properties:
                        args:
                          description: |-
                            Arguments to the entrypoint.
                            The container image's CMD is used if this is not provided.
                            Variable references $(VAR_NAME) are expanded using the container's environment. If a variable
                            cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced
                            to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. "$$(VAR_NAME)" will
                            produce the string literal "$(VAR_NAME)". Escaped references will never be expanded, regardless
                            of whether the variable exists or not. Cannot be updated.
                            More info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        command:
                          description: |-
                            Entrypoint array. Not executed within a shell.
                            The container image's ENTRYPOINT is used if this is not provided.
                            Variable references $(VAR_NAME) are expanded using the container's environment. If a variable
                            cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced
                            to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. "$$(VAR_NAME)" will
                            produce the string literal "$(VAR_NAME)". Escaped references will never be expanded, regardless
                            of whether the variable exists or not. Cannot be updated.
                            More info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        env:
                          description: |-
                            List of environment variables to set in the container.
                            Cannot be updated.
                          items:
                            description: EnvVar represents an environment variable
                              present in a Container.
                            properties:
                              name:
                                description: Name of the environment variable. Must
                                  be a C_IDENTIFIER.
                                type: string
                              value:
                                description: |-
                                  Variable references $(VAR_NAME) are expanded
                                  using the previously defined environment variables in the container and
                                  any service environment variables. If a variable cannot be resolved,
                                  the reference in the input string will be unchanged. Double $$ are reduced
                                  to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e.
                                  "$$(VAR_NAME)" will produce the string literal "$(VAR_NAME)".
                                  Escaped references will never be expanded, regardless of whether the variable
                                  exists or not.
                                  Defaults to "".
                                type: string
                              valueFrom:
                                description: Source for the environment variable's
                                  value. Cannot be used if value is not empty.
                                properties:
                                  configMapKeyRef:
                                    description: Selects a key of a ConfigMap.
                                    properties:
                                      key:
                                        description: The key to select.
                                        type: string
                                      name:
                                        default: ""
                                        description: |-
                                          Name of the referent.
                                          This field is effectively required, but due to backwards compatibility is
                                          allowed to be empty. Instances of this type with an empty value here are
                                          almost certainly wrong.
                                          More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                        type: string
                                      optional:
                                        description: Specify whether the ConfigMap
                                          or its key must be defined
                                        type: boolean
                                    required:
                                    - key
                                    type: object
                                    x-kubernetes-map-type: atomic
                                  fieldRef:
                                    description: |-
                                      Selects a field of the pod: supports metadata.name, metadata.namespace, `metadata.labels['<KEY>']`, `metadata.annotations['<KEY>']`,
                                      spec.nodeName, spec.serviceAccountName, status.hostIP, status.podIP, status.podIPs.
                                    properties:
                                      apiVersion:
                                        description: Version of the schema the FieldPath
                                          is written in terms of, defaults to "v1".
                                        type: string
                                      fieldPath:
                                        description: Path of the field to select in
                                          the specified API version.
                                        type: string
                                    required:
                                    - fieldPath
                                    type: object
                                    x-kubernetes-map-type: atomic
                                  resourceFieldRef:
                                    description: |-
                                      Selects a resource of the container: only resources limits and requests
                                      (limits.cpu, limits.memory, limits.ephemeral-storage, requests.cpu, requests.memory and requests.ephemeral-storage) are currently supported.
                                    properties:
                                      containerName:
                                        description: 'Container name: required for
                                          volumes, optional for env vars'
                                        type: string
                                      divisor:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        description: Specifies the output format of
                                          the exposed resources, defaults to "1"
                                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                        x-kubernetes-int-or-string: true
                                      resource:
                                        description: 'Required: resource to select'
                                        type: string
                                    required:
                                    - resource
                                    type: object
                                    x-kubernetes-map-type: atomic
                                  secretKeyRef:
                                    description: Selects a key of a secret in the
                                      pod's namespace
                                    properties:
                                      key:
                                        description: The key of the secret to select
                                          from.  Must be a valid secret key.
                                        type: string
                                      name:
                                        default: ""
                                        description: |-
                                          Name of the referent.
                                          This field is effectively required, but due to backwards compatibility is
                                          allowed to be empty. Instances of this type with an empty value here are
                                          almost certainly wrong.
                                          More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                        type: string
                                      optional:
                                        description: Specify whether the Secret or
                                          its key must be defined
                                        type: boolean
                                    required:
                                    - key
                                    type: object
                                    x-kubernetes-map-type: atomic
                                type: object
                            required:
                            - name
                            type: object
                          type: array
                          x-kubernetes-list-map-keys:
                          - name
                          x-kubernetes-list-type: map
                        envFrom:
                          description: |-
                            List of sources to populate environment variables in the container.
                            The keys defined within a source must be a C_IDENTIFIER. All invalid keys
                            will be reported as an event when the container is starting. When a key exists in multiple
                            sources, the value associated with the last source will take precedence.
                            Values defined by an Env with a duplicate key will take precedence.
                            Cannot be updated.
                          items:
                            description: EnvFromSource represents the source of a
                              set of ConfigMaps or Secrets
                            properties:
                              configMapRef:
                                description: The ConfigMap to select from
                                properties:
                                  name:
                                    default: ""
                                    description: |-
                                      Name of the referent.
                                      This field is effectively required, but due to backwards compatibility is
                                      allowed to be empty. Instances of this type with an empty value here are
                                      almost certainly wrong.
                                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                    type: string
                                  optional:
                                    description: Specify whether the ConfigMap must
                                      be defined
                                    type: boolean
                                type: object
                                x-kubernetes-map-type: atomic
                              prefix:
                                description: Optional text to prepend to the name
                                  of each environment variable. Must be a C_IDENTIFIER.
                                type: string
                              secretRef:
                                description: The Secret to select from
                                properties:
                                  name:
                                    default: ""
                                    description: |-
                                      Name of the referent.
                                      This field is effectively required, but due to backwards compatibility is
                                      allowed to be empty. Instances of this type with an empty value here are
                                      almost certainly wrong.
                                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                    type: string
                                  optional:
                                    description: Specify whether the Secret must be
                                      defined
                                    type: boolean
                                type: object
                                x-kubernetes-map-type: atomic
                            type: object
                          type: array
                          x-kubernetes-list-type: atomic
                        image:
                          description: Image that is used to spawn container if present
                            will override base config
                          type: string
                        name:
                          description: |-
                            Name of the container specified as a DNS_LABEL.
                            Each container in a pod must have a unique name (DNS_LABEL).
                            Cannot be updated.
                          type: string
                        resources:
                          description: |-
                            Compute Resources required by this container.
                            Cannot be updated.
                            More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                          properties:
                            claims:
                              description: |-
                                Claims lists the names of resources, defined in spec.resourceClaims,
                                that are used by this container.

                                This is an alpha field and requires enabling the
                                DynamicResourceAllocation feature gate.

                                This field is immutable. It can only be set for containers.
                              items:
                                description: ResourceClaim references one entry in
                                  PodSpec.ResourceClaims.
                                properties:
                                  name:
                                    description: |-
                                      Name must match the name of one entry in pod.spec.resourceClaims of
                                      the Pod where this field is used. It makes that resource available
                                      inside a container.
                                    type: string
                                  request:
                                    description: |-
                                      Request is the name chosen for a request in the referenced claim.
                                      If empty, everything from the claim is made available, otherwise
                                      only the result of this request.
                                    type: string
                                required:
                                - name
                                type: object
                              type: array
                              x-kubernetes-list-map-keys:
                              - name
                              x-kubernetes-list-type: map
                            limits:
                              additionalProperties:
                                anyOf:
                                - type: integer
                                - type: string
                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                x-kubernetes-int-or-string: true
                              description: |-
                                Limits describes the maximum amount of compute resources allowed.
                                More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                              type: object
                            requests:
                              additionalProperties:
                                anyOf:
                                - type: integer
                                - type: string
                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                x-kubernetes-int-or-string: true
                              description: |-
                                Requests describes the minimum amount of compute resources required.
                                If Requests is omitted for a container, it defaults to Limits if that is explicitly specified,
                                otherwise to an implementation-defined value. Requests cannot exceed Limits.
                                More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                              type: object
                          type: object
                      required:
                      - name
                      type: object
                    type: array
                  replicas:
                    default: 1
                    description: Replicas defines the desired number of replicas for
                      this deployment.
                    format: int32
                    minimum: 0
                    nullable: true
                    type: integer
                type: object
              modelArtifacts:
                description: |-
                  modelArtifacts provides information needed to download artifacts
                  needed to serve a model
                properties:
                  authSecretName:
                    description: Name of the authentication secret. Contains HF_TOKEN
                    type: string
                  size:
                    anyOf:
                    - type: integer
                    - type: string
                    description: |-
                      Size of the model artifacts on disk
                      ensure Size is large enough when providing hf://... URI
                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                    x-kubernetes-int-or-string: true
                  uri:
                    description: |-
                      URI is the model URI
                      Three types of URIs are support to enable models packaged as images (oci://<image-repo>/<image-name><:image-tag>),
                      models downloaded from HuggingFace (hf://<model-repo>/<model-name>)
                      and pre-existing models loaded from a volume-mounted PVC (pvc://model-path)
                    type: string
                required:
                - uri
                type: object
              prefill:
                description: Prefill is the prefill portion of the spec
                properties:
                  acceleratorTypes:
                    description: |-
                      pod
                      AcceleratorTypes determines the set of accelerators on which
                      this pod will be run. Any matching accelerator type can be used
                      to place the model pods.This will override base config when present
                    properties:
                      labelKey:
                        description: |-
                          node label key that identifies the accelerator type for the node
                          e.g., nvidia.com/gpu.product
                        type: string
                      labelValues:
                        description: |-
                          node label values that will be matched against for pod scheduling.
                          e.g., [A100, H100]
                        items:
                          type: string
                        minItems: 1
                        type: array
                    required:
                    - labelKey
                    - labelValues
                    type: object
                  containers:
                    description: Container holds vllm container container details
                      that will be overridden from base config when present.
                    items:
                      description: ContainerSpec defines container-level configuration.
                      properties:
                        args:
                          description: |-
                            Arguments to the entrypoint.
                            The container image's CMD is used if this is not provided.
                            Variable references $(VAR_NAME) are expanded using the container's environment. If a variable
                            cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced
                            to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. "$$(VAR_NAME)" will
                            produce the string literal "$(VAR_NAME)". Escaped references will never be expanded, regardless
                            of whether the variable exists or not. Cannot be updated.
                            More info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        command:
                          description: |-
                            Entrypoint array. Not executed within a shell.
                            The container image's ENTRYPOINT is used if this is not provided.
                            Variable references $(VAR_NAME) are expanded using the container's environment. If a variable
                            cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced
                            to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. "$$(VAR_NAME)" will
                            produce the string literal "$(VAR_NAME)". Escaped references will never be expanded, regardless
                            of whether the variable exists or not. Cannot be updated.
                            More info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        env:
                          description: |-
                            List of environment variables to set in the container.
                            Cannot be updated.
                          items:
                            description: EnvVar represents an environment variable
                              present in a Container.
                            properties:
                              name:
                                description: Name of the environment variable. Must
                                  be a C_IDENTIFIER.
                                type: string
                              value:
                                description: |-
                                  Variable references $(VAR_NAME) are expanded
                                  using the previously defined environment variables in the container and
                                  any service environment variables. If a variable cannot be resolved,
                                  the reference in the input string will be unchanged. Double $$ are reduced
                                  to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e.
                                  "$$(VAR_NAME)" will produce the string literal "$(VAR_NAME)".
                                  Escaped references will never be expanded, regardless of whether the variable
                                  exists or not.
                                  Defaults to "".
                                type: string
                              valueFrom:
                                description: Source for the environment variable's
                                  value. Cannot be used if value is not empty.
                                properties:
                                  configMapKeyRef:
                                    description: Selects a key of a ConfigMap.
                                    properties:
                                      key:
                                        description: The key to select.
                                        type: string
                                      name:
                                        default: ""
                                        description: |-
                                          Name of the referent.
                                          This field is effectively required, but due to backwards compatibility is
                                          allowed to be empty. Instances of this type with an empty value here are
                                          almost certainly wrong.
                                          More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                        type: string
                                      optional:
                                        description: Specify whether the ConfigMap
                                          or its key must be defined
                                        type: boolean
                                    required:
                                    - key
                                    type: object
                                    x-kubernetes-map-type: atomic
                                  fieldRef:
                                    description: |-
                                      Selects a field of the pod: supports metadata.name, metadata.namespace, `metadata.labels['<KEY>']`, `metadata.annotations['<KEY>']`,
                                      spec.nodeName, spec.serviceAccountName, status.hostIP, status.podIP, status.podIPs.
                                    properties:
                                      apiVersion:
                                        description: Version of the schema the FieldPath
                                          is written in terms of, defaults to "v1".
                                        type: string
                                      fieldPath:
                                        description: Path of the field to select in
                                          the specified API version.
                                        type: string
                                    required:
                                    - fieldPath
                                    type: object
                                    x-kubernetes-map-type: atomic
                                  resourceFieldRef:
                                    description: |-
                                      Selects a resource of the container: only resources limits and requests
                                      (limits.cpu, limits.memory, limits.ephemeral-storage, requests.cpu, requests.memory and requests.ephemeral-storage) are currently supported.
                                    properties:
                                      containerName:
                                        description: 'Container name: required for
                                          volumes, optional for env vars'
                                        type: string
                                      divisor:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        description: Specifies the output format of
                                          the exposed resources, defaults to "1"
                                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                        x-kubernetes-int-or-string: true
                                      resource:
                                        description: 'Required: resource to select'
                                        type: string
                                    required:
                                    - resource
                                    type: object
                                    x-kubernetes-map-type: atomic
                                  secretKeyRef:
                                    description: Selects a key of a secret in the
                                      pod's namespace
                                    properties:
                                      key:
                                        description: The key of the secret to select
                                          from.  Must be a valid secret key.
                                        type: string
                                      name:
                                        default: ""
                                        description: |-
                                          Name of the referent.
                                          This field is effectively required, but due to backwards compatibility is
                                          allowed to be empty. Instances of this type with an empty value here are
                                          almost certainly wrong.
                                          More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                        type: string
                                      optional:
                                        description: Specify whether the Secret or
                                          its key must be defined
                                        type: boolean
                                    required:
                                    - key
                                    type: object
                                    x-kubernetes-map-type: atomic
                                type: object
                            required:
                            - name
                            type: object
                          type: array
                          x-kubernetes-list-map-keys:
                          - name
                          x-kubernetes-list-type: map
                        envFrom:
                          description: |-
                            List of sources to populate environment variables in the container.
                            The keys defined within a source must be a C_IDENTIFIER. All invalid keys
                            will be reported as an event when the container is starting. When a key exists in multiple
                            sources, the value associated with the last source will take precedence.
                            Values defined by an Env with a duplicate key will take precedence.
                            Cannot be updated.
                          items:
                            description: EnvFromSource represents the source of a
                              set of ConfigMaps or Secrets
                            properties:
                              configMapRef:
                                description: The ConfigMap to select from
                                properties:
                                  name:
                                    default: ""
                                    description: |-
                                      Name of the referent.
                                      This field is effectively required, but due to backwards compatibility is
                                      allowed to be empty. Instances of this type with an empty value here are
                                      almost certainly wrong.
                                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                    type: string
                                  optional:
                                    description: Specify whether the ConfigMap must
                                      be defined
                                    type: boolean
                                type: object
                                x-kubernetes-map-type: atomic
                              prefix:
                                description: Optional text to prepend to the name
                                  of each environment variable. Must be a C_IDENTIFIER.
                                type: string
                              secretRef:
                                description: The Secret to select from
                                properties:
                                  name:
                                    default: ""
                                    description: |-
                                      Name of the referent.
                                      This field is effectively required, but due to backwards compatibility is
                                      allowed to be empty. Instances of this type with an empty value here are
                                      almost certainly wrong.
                                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                    type: string
                                  optional:
                                    description: Specify whether the Secret must be
                                      defined
                                    type: boolean
                                type: object
                                x-kubernetes-map-type: atomic
                            type: object
                          type: array
                          x-kubernetes-list-type: atomic
                        image:
                          description: Image that is used to spawn container if present
                            will override base config
                          type: string
                        name:
                          description: |-
                            Name of the container specified as a DNS_LABEL.
                            Each container in a pod must have a unique name (DNS_LABEL).
                            Cannot be updated.
                          type: string
                        resources:
                          description: |-
                            Compute Resources required by this container.
                            Cannot be updated.
                            More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                          properties:
                            claims:
                              description: |-
                                Claims lists the names of resources, defined in spec.resourceClaims,
                                that are used by this container.

                                This is an alpha field and requires enabling the
                                DynamicResourceAllocation feature gate.

                                This field is immutable. It can only be set for containers.
                              items:
                                description: ResourceClaim references one entry in
                                  PodSpec.ResourceClaims.
                                properties:
                                  name:
                                    description: |-
                                      Name must match the name of one entry in pod.spec.resourceClaims of
                                      the Pod where this field is used. It makes that resource available
                                      inside a container.
                                    type: string
                                  request:
                                    description: |-
                                      Request is the name chosen for a request in the referenced claim.
                                      If empty, everything from the claim is made available, otherwise
                                      only the result of this request.
                                    type: string
                                required:
                                - name
                                type: object
                              type: array
                              x-kubernetes-list-map-keys:
                              - name
                              x-kubernetes-list-type: map
                            limits:
                              additionalProperties:
                                anyOf:
                                - type: integer
                                - type: string
                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                x-kubernetes-int-or-string: true
                              description: |-
                                Limits describes the maximum amount of compute resources allowed.
                                More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                              type: object
                            requests:
                              additionalProperties:
                                anyOf:
                                - type: integer
                                - type: string
                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                x-kubernetes-int-or-string: true
                              description: |-
                                Requests describes the minimum amount of compute resources required.
                                If Requests is omitted for a container, it defaults to Limits if that is explicitly specified,
                                otherwise to an implementation-defined value. Requests cannot exceed Limits.
                                More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                              type: object
                          type: object
                      required:
                      - name
                      type: object
                    type: array
                  initContainers:
                    description: InitContainers holds vllm init container details
                      that will be overridden from base config when present.
                    items:
                      description: ContainerSpec defines container-level configuration.
                      properties:
                        args:
                          description: |-
                            Arguments to the entrypoint.
                            The container image's CMD is used if this is not provided.
                            Variable references $(VAR_NAME) are expanded using the container's environment. If a variable
                            cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced
                            to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. "$$(VAR_NAME)" will
                            produce the string literal "$(VAR_NAME)". Escaped references will never be expanded, regardless
                            of whether the variable exists or not. Cannot be updated.
                            More info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        command:
                          description: |-
                            Entrypoint array. Not executed within a shell.
                            The container image's ENTRYPOINT is used if this is not provided.
                            Variable references $(VAR_NAME) are expanded using the container's environment. If a variable
                            cannot be resolved, the reference in the input string will be unchanged. Double $$ are reduced
                            to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e. "$$(VAR_NAME)" will
                            produce the string literal "$(VAR_NAME)". Escaped references will never be expanded, regardless
                            of whether the variable exists or not. Cannot be updated.
                            More info: https://kubernetes.io/docs/tasks/inject-data-application/define-command-argument-container/#running-a-command-in-a-shell
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        env:
                          description: |-
                            List of environment variables to set in the container.
                            Cannot be updated.
                          items:
                            description: EnvVar represents an environment variable
                              present in a Container.
                            properties:
                              name:
                                description: Name of the environment variable. Must
                                  be a C_IDENTIFIER.
                                type: string
                              value:
                                description: |-
                                  Variable references $(VAR_NAME) are expanded
                                  using the previously defined environment variables in the container and
                                  any service environment variables. If a variable cannot be resolved,
                                  the reference in the input string will be unchanged. Double $$ are reduced
                                  to a single $, which allows for escaping the $(VAR_NAME) syntax: i.e.
                                  "$$(VAR_NAME)" will produce the string literal "$(VAR_NAME)".
                                  Escaped references will never be expanded, regardless of whether the variable
                                  exists or not.
                                  Defaults to "".
                                type: string
                              valueFrom:
                                description: Source for the environment variable's
                                  value. Cannot be used if value is not empty.
                                properties:
                                  configMapKeyRef:
                                    description: Selects a key of a ConfigMap.
                                    properties:
                                      key:
                                        description: The key to select.
                                        type: string
                                      name:
                                        default: ""
                                        description: |-
                                          Name of the referent.
                                          This field is effectively required, but due to backwards compatibility is
                                          allowed to be empty. Instances of this type with an empty value here are
                                          almost certainly wrong.
                                          More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                        type: string
                                      optional:
                                        description: Specify whether the ConfigMap
                                          or its key must be defined
                                        type: boolean
                                    required:
                                    - key
                                    type: object
                                    x-kubernetes-map-type: atomic
                                  fieldRef:
                                    description: |-
                                      Selects a field of the pod: supports metadata.name, metadata.namespace, `metadata.labels['<KEY>']`, `metadata.annotations['<KEY>']`,
                                      spec.nodeName, spec.serviceAccountName, status.hostIP, status.podIP, status.podIPs.
                                    properties:
                                      apiVersion:
                                        description: Version of the schema the FieldPath
                                          is written in terms of, defaults to "v1".
                                        type: string
                                      fieldPath:
                                        description: Path of the field to select in
                                          the specified API version.
                                        type: string
                                    required:
                                    - fieldPath
                                    type: object
                                    x-kubernetes-map-type: atomic
                                  resourceFieldRef:
                                    description: |-
                                      Selects a resource of the container: only resources limits and requests
                                      (limits.cpu, limits.memory, limits.ephemeral-storage, requests.cpu, requests.memory and requests.ephemeral-storage) are currently supported.
                                    properties:
                                      containerName:
                                        description: 'Container name: required for
                                          volumes, optional for env vars'
                                        type: string
                                      divisor:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        description: Specifies the output format of
                                          the exposed resources, defaults to "1"
                                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                        x-kubernetes-int-or-string: true
                                      resource:
                                        description: 'Required: resource to select'
                                        type: string
                                    required:
                                    - resource
                                    type: object
                                    x-kubernetes-map-type: atomic
                                  secretKeyRef:
                                    description: Selects a key of a secret in the
                                      pod's namespace
                                    properties:
                                      key:
                                        description: The key of the secret to select
                                          from.  Must be a valid secret key.
                                        type: string
                                      name:
                                        default: ""
                                        description: |-
                                          Name of the referent.
                                          This field is effectively required, but due to backwards compatibility is
                                          allowed to be empty. Instances of this type with an empty value here are
                                          almost certainly wrong.
                                          More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                        type: string
                                      optional:
                                        description: Specify whether the Secret or
                                          its key must be defined
                                        type: boolean
                                    required:
                                    - key
                                    type: object
                                    x-kubernetes-map-type: atomic
                                type: object
                            required:
                            - name
                            type: object
                          type: array
                          x-kubernetes-list-map-keys:
                          - name
                          x-kubernetes-list-type: map
                        envFrom:
                          description: |-
                            List of sources to populate environment variables in the container.
                            The keys defined within a source must be a C_IDENTIFIER. All invalid keys
                            will be reported as an event when the container is starting. When a key exists in multiple
                            sources, the value associated with the last source will take precedence.
                            Values defined by an Env with a duplicate key will take precedence.
                            Cannot be updated.
                          items:
                            description: EnvFromSource represents the source of a
                              set of ConfigMaps or Secrets
                            properties:
                              configMapRef:
                                description: The ConfigMap to select from
                                properties:
                                  name:
                                    default: ""
                                    description: |-
                                      Name of the referent.
                                      This field is effectively required, but due to backwards compatibility is
                                      allowed to be empty. Instances of this type with an empty value here are
                                      almost certainly wrong.
                                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                    type: string
                                  optional:
                                    description: Specify whether the ConfigMap must
                                      be defined
                                    type: boolean
                                type: object
                                x-kubernetes-map-type: atomic
                              prefix:
                                description: Optional text to prepend to the name
                                  of each environment variable. Must be a C_IDENTIFIER.
                                type: string
                              secretRef:
                                description: The Secret to select from
                                properties:
                                  name:
                                    default: ""
                                    description: |-
                                      Name of the referent.
                                      This field is effectively required, but due to backwards compatibility is
                                      allowed to be empty. Instances of this type with an empty value here are
                                      almost certainly wrong.
                                      More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                    type: string
                                  optional:
                                    description: Specify whether the Secret must be
                                      defined
                                    type: boolean
                                type: object
                                x-kubernetes-map-type: atomic
                            type: object
                          type: array
                          x-kubernetes-list-type: atomic
                        image:
                          description: Image that is used to spawn container if present
                            will override base config
                          type: string
                        name:
                          description: |-
                            Name of the container specified as a DNS_LABEL.
                            Each container in a pod must have a unique name (DNS_LABEL).
                            Cannot be updated.
                          type: string
                        resources:
                          description: |-
                            Compute Resources required by this container.
                            Cannot be updated.
                            More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                          properties:
                            claims:
                              description: |-
                                Claims lists the names of resources, defined in spec.resourceClaims,
                                that are used by this container.

                                This is an alpha field and requires enabling the
                                DynamicResourceAllocation feature gate.

                                This field is immutable. It can only be set for containers.
                              items:
                                description: ResourceClaim references one entry in
                                  PodSpec.ResourceClaims.
                                properties:
                                  name:
                                    description: |-
                                      Name must match the name of one entry in pod.spec.resourceClaims of
                                      the Pod where this field is used. It makes that resource available
                                      inside a container.
                                    type: string
                                  request:
                                    description: |-
                                      Request is the name chosen for a request in the referenced claim.
                                      If empty, everything from the claim is made available, otherwise
                                      only the result of this request.
                                    type: string
                                required:
                                - name
                                type: object
                              type: array
                              x-kubernetes-list-map-keys:
                              - name
                              x-kubernetes-list-type: map
                            limits:
                              additionalProperties:
                                anyOf:
                                - type: integer
                                - type: string
                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                x-kubernetes-int-or-string: true
                              description: |-
                                Limits describes the maximum amount of compute resources allowed.
                                More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                              type: object
                            requests:
                              additionalProperties:
                                anyOf:
                                - type: integer
                                - type: string
                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                x-kubernetes-int-or-string: true
                              description: |-
                                Requests describes the minimum amount of compute resources required.
                                If Requests is omitted for a container, it defaults to Limits if that is explicitly specified,
                                otherwise to an implementation-defined value. Requests cannot exceed Limits.
                                More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                              type: object
                          type: object
                      required:
                      - name
                      type: object
                    type: array
                  parallelism:
                    description: |-
                      vllm
                      Parallelism specifies vllm parallelism that will be overridden from base config when present.
                    properties:
                      tensor:
                        default: 1
                        description: |-
                          TensorParallelism corresponds to the same argument in vllm
                          This also corresponds to number of GPUs
                        format: int32
                        minimum: 0
                        nullable: true
                        type: integer
                    type: object
                  replicas:
                    default: 1
                    description: Replicas defines the desired number of replicas for
                      this deployment.
                    format: int32
                    minimum: 0
                    nullable: true
                    type: integer
                type: object
              routing:
                description: Routing provides information needed to create configuration
                  for routing
                properties:
                  modelName:
                    description: |-
                      // CreateInferencePool indicates if inference pool resource will be created
                      CreateInferencePool bool `json:"createInferencePool"`

                      ModelName is the model field within inference request
                      This should be unique across ModelService objects.

                      If the name is reused, an error will be
                      shown on the status of a ModelService that attempted to reuse.
                      The oldest ModelService, based on creation timestamp, will be selected
                      to remain valid. In the event of a race condition, one will be selected
                      arbitrarily.

                      refer to https://gateway-api-inference-extension.sigs.k8s.io
                      for relationship between model name, inference pool, and inference model

                      From GIE:
                      ModelName is the name of the model as it will be set in the "model" parameter for an incoming request.
                      ModelNames must be unique for a referencing InferencePool
                      (names can be reused for a different pool in the same cluster).
                      The modelName with the oldest creation timestamp is retained, and the incoming
                      InferenceModel is sets the Ready status to false with a corresponding reason.
                      In the rare case of a race condition, one Model will be selected randomly to be considered valid, and the other rejected.
                      Names can be reserved without an underlying model configured in the pool.
                      This can be done by specifying a target model and setting the weight to zero,
                      an error will be returned specifying that no valid target model is found.
                    maxLength: 256
                    type: string
                    x-kubernetes-validations:
                    - message: modelName is immutable
                      rule: self == oldSelf
                  ports:
                    description: |-
                      Ports is a list of named ports
                      These can be referenced by name in configuration of base configuration or model services
                    items:
                      properties:
                        name:
                          description: Name that can be used in place of port number
                            in templates
                          type: string
                        port:
                          description: Value of port
                          format: int32
                          minimum: 1
                          type: integer
                      required:
                      - name
                      - port
                      type: object
                    type: array
                required:
                - modelName
                type: object
            required:
            - modelArtifacts
            - routing
            type: object
          status:
            description: ModelServiceStatus defines the observed state of ModelService
            properties:
              conditions:
                description: |-
                  Combined deployment conditions from prefill and decode deployments
                  Condition types should be prefixed to indicate their origin
                  Example types: "PrefillAvailable", "DecodeProgressing", etc.
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              configMapNames:
                description: |-
                  ConfigMapNames identifies the configmap used for prefill and decode
                  if ConfigMapNames is yet to be created,
                  this reference will be an empty list
                items:
                  type: string
                type: array
              decodeAvailable:
                format: int32
                type: integer
              decodeDeploymentRef:
                description: |-
                  DecodeDeploymentRef identifies the decode deployment
                  if decode deployment is yet to be created,
                  this reference will be nil
                type: string
              decodeReady:
                description: READY and AVAILABLE for decode
                type: string
              decodeServiceAccountRef:
                description: |-
                  DecodeServiceAccountRef identifies the service account for decode
                  if DecodeServiceAccountRef is yet to be created,
                  this reference will be nil
                type: string
              eppAvailable:
                format: int32
                type: integer
              eppDeploymentRef:
                description: |-
                  EppDeploymentRef identifies the epp deployment
                  if epp deployment is yet to be created,
                  this reference will be nil
                type: string
              eppReady:
                description: READY and AVAILABLE for Epp
                type: string
              eppRoleBinding:
                description: |-
                  EppRoleBinding identifies the rolebinding for Epp
                  if EppRoleBinding is yet to be created,
                  this reference will be nil
                type: string
              inferenceModelRef:
                description: |-
                  InferenceModelRef identifies the inference model resource
                  if inference model is yet to be created,
                  this reference will be nil
                type: string
              inferencePoolRef:
                description: |-
                  InferencePoolRef identifies the inference pool resource
                  if inference pool is yet to be created,
                  this reference will be nil
                type: string
              prefillAvailable:
                format: int32
                type: integer
              prefillDeploymentRef:
                description: |-
                  PrefillDeploymentRef identifies the prefill deployment
                  if prefill stanza is omitted, or if prefill deployment is yet to be created,
                  this reference will be nil
                type: string
              prefillReady:
                description: READY and AVAILABLE for prefill
                type: string
              prefillServiceAccountRef:
                description: |-
                  PDServiceAccountRef identifies the service account for PD
                  if PDServiceAccountRef is yet to be created,
                  this reference will be nil
                type: string
            required:
            - decodeAvailable
            - decodeReady
            - eppAvailable
            - eppReady
            - prefillAvailable
            - prefillReady
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
