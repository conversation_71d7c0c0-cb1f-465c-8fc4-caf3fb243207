Thank you for installing {{ .Chart.Name }}.

Your release is named `{{ .Release.Name }}`.

To learn more about the release, try:

```bash
$ helm status {{ .Release.Name }}
$ helm get all {{ .Release.Name }}
```

{{ if .Values.modelservice.enabled }}
Following presets are available to your users:

{{ $nameWidth := 65 }}
{{ $descriptionWidth := 95 }}
{{ include "tableRowPrinter" ( dict "nameWidth" $nameWidth "descriptionWidth" $descriptionWidth "name" "Name" "description" "Description" ) }}
{{ include "tableRowPrinter" ( dict "nameWidth" $nameWidth "descriptionWidth" $descriptionWidth "spacer" "-" ) }}
{{ include "tableRowPrinter" ( dict "nameWidth" $nameWidth "descriptionWidth" $descriptionWidth "name" "basic-gpu-preset" "description" "Basic gpu inference" ) }}
{{ include "tableRowPrinter" ( dict "nameWidth" $nameWidth "descriptionWidth" $descriptionWidth "name" "basic-gpu-with-nixl-preset" "description" "GPU inference with NIXL P/D KV transfer and cache offloading" ) }}
{{ include "tableRowPrinter" ( dict "nameWidth" $nameWidth "descriptionWidth" $descriptionWidth "name" "basic-gpu-with-nixl-and-redis-lookup-preset" "description" "GPU inference with NIXL P/D KV transfer, cache offloading and Redis lookup server" ) }}
{{ include "tableRowPrinter" ( dict "nameWidth" $nameWidth "descriptionWidth" $descriptionWidth "name" "basic-sim-preset" "description" "Basic simulation" ) }}

{{ if not .Values.sampleApplication.enabled  -}}
To provision a `ModelService`, create a new CR:

```yaml
apiVersion: llm-d.ai/v1alpha1
kind: ModelService
metadata:
  name: <name>
spec:
  decoupleScaling: false
  baseConfigMapRef:
    name: basic-gpu-with-nixl-and-redis-lookup-preset
  routing:
    modelName: <model_name>
  modelArtifacts:
    uri: pvc://<pvc_name>/<path_to_model>
  decode:
    replicas: 1
    containers:
    - name: "vllm"
      args:
      - "--model"
      - <model_name>
  prefill:
    replicas: 1
    containers:
    - name: "vllm"
      args:
      - "--model"
      - <model_name>
```
{{- end }}
{{- end }}
