{{ if .Values.modelservice.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "modelservice.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "common.labels.standard" . | nindent 4 }}
    control-plane: controller-manager
    app.kubernetes.io/component: modelservice
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  annotations:
    {{- if .Values.commonAnnotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
    {{- end }}
    {{- if .Values.modelservice.annotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.annotations "context" $) | nindent 4 }}
    {{- end }}
spec:
  replicas: {{ .Values.modelservice.replicas | default 1 }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" . | nindent 6 }}
      control-plane: controller-manager
      app.kubernetes.io/component: modelservice
  template:
    metadata:
      labels: {{- include "common.labels.standard" . | nindent 8 }}
        control-plane: controller-manager
        app.kubernetes.io/component: modelservice
        {{- if .Values.modelservice.podLabels }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.podLabels "context" $ ) | nindent 8 }}
        {{- end }}
      annotations:
        {{- if .Values.modelservice.podAnnotations }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.podAnnotations "context" $ ) | nindent 8 }}
        {{- end }}
    spec:
      {{- if .Values.modelservice.podSecurityContext }}
      securityContext:
        {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.podSecurityContext "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.modelservice.affinity }}
      affinity:
        {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.affinity "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.modelservice.topologySpreadConstraints }}
      topologySpreadConstraints:
        {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.topologySpreadConstraints "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.modelservice.nodeSelector }}
      nodeSelector:
        {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.modelservice.tolerations }}
      tolerations:
        {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.tolerations "context" $) | nindent 8 }}
      {{- end }}
      containers:
      - args:
        - --leader-elect=false
        - --health-probe-bind-address=:8081
        - --epp-cluster-role
        - {{ include "modelservice.fullname" . }}-endpoint-picker
        - --epp-pull-secrets
        - {{ include "common.images.renderImagePullSecretsString" (dict "images" (list .Values.modelservice.epp.image) "context" $) }}
        - --pd-pull-secrets
        - {{ include "common.images.renderImagePullSecretsString" (dict "images" (list .Values.modelservice.vllm.image) "context" $) }}
        command:
        - /manager
        image: {{ include "modelservice.image" . }}
        imagePullPolicy: {{ .Values.modelservice.image.imagePullPolicy }}
        {{- if .Values.modelservice.containerSecurityContext }}
        securityContext:
          {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.containerSecurityContext "context" $) | nindent 10 }}
        {{- end }}
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8081
          initialDelaySeconds: 15
          periodSeconds: 20
        name: manager
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 10
        resources:
          requests:
            memory: "100Mi"
            cpu: "100m"
          limits:
            memory: "250Mi"
            cpu: "250m"
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
      securityContext:
        runAsNonRoot: true
        seccompProfile:
          type: RuntimeDefault
      serviceAccountName: {{ include "modelservice.serviceAccountName" . }}
{{- end}}
