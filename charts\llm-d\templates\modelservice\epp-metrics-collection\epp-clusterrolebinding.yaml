{{- if and .Values.sampleApplication.enabled .Values.modelservice.enabled .Values.modelservice.epp.metrics.enabled }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "modelservice.serviceAccountName" . }}-epp-metrics-scrape
  labels:
    {{- include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  annotations:
    {{- if .Values.commonAnnotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
    {{- end }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "modelservice.fullname" . }}-epp-metrics-scrape
subjects:
- kind: ServiceAccount
  name: {{ include "modelservice.serviceAccountName" . }}-epp-metrics-scrape
  namespace: {{ .Release.Namespace }}
{{- end }}
