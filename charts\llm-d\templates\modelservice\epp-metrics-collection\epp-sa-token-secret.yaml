{{- if and .Values.sampleApplication.enabled .Values.modelservice.enabled .Values.modelservice.epp.metrics.enabled }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "modelservice.serviceAccountName" . }}-epp-metrics-scrape-token
  labels:
    {{- include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  annotations:
    {{- if .Values.commonAnnotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
    {{- end }}
    kubernetes.io/service-account.name: {{ include "modelservice.serviceAccountName" . }}-epp-metrics-scrape
type: kubernetes.io/service-account-token
{{- end }}
