{{- if and .Values.sampleApplication.enabled .Values.modelservice.enabled .Values.modelservice.epp.metrics.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ include "modelservice.serviceAccountName" . }}-epp-monitor
  labels: {{ include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  annotations:
    {{- if .Values.commonAnnotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
    {{- end }}
spec:
  endpoints:
  - interval: {{ .Values.modelservice.epp.metrics.serviceMonitor.interval }}
    port: {{ .Values.modelservice.epp.metrics.serviceMonitor.port }}
    path: {{ .Values.modelservice.epp.metrics.serviceMonitor.path }}
    authorization:
      credentials:
        key: token
        name: {{ include "modelservice.serviceAccountName" . }}-epp-metrics-scrape-token
  jobLabel: {{ include "modelservice.serviceAccountName" . }}-epp
  namespaceSelector:
    {{- if .Values.modelservice.epp.metrics.serviceMonitor.namespaceSelector.any }}
    any: true
    {{- else }}
    matchNames:
    - {{ .Release.Namespace }}
    {{- if .Values.modelservice.epp.metrics.serviceMonitor.namespaceSelector.matchNames }}
    {{- range .Values.modelservice.epp.metrics.serviceMonitor.namespaceSelector.matchNames }}
    - {{ . }}
    {{- end }}
    {{- end }}
    {{- end }}
  selector:
    matchLabels:
      {{- if .Values.modelservice.epp.metrics.serviceMonitor.selector.matchLabels }}
      {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.epp.metrics.serviceMonitor.selector.matchLabels "context" $ ) | nindent 4 }}
      {{- end }}
      {{ include "metrics.label" . }}
{{- end }}
