{{ if and .Values.modelservice.enabled .Values.modelservice.metrics.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ include "modelservice.fullname" . }}-monitor
  labels: {{ include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: modelservice
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
    {{- if .Values.modelservice.metrics.serviceMonitor.labels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.metrics.serviceMonitor.labels "context" $ ) | nindent 4 }}
    {{- end }}
  annotations:
    {{- if .Values.commonAnnotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
    {{- end }}
    {{- if .Values.modelservice.metrics.serviceMonitor.annotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.metrics.serviceMonitor.annotations "context" $ ) | nindent 4 }}
    {{- end }}
spec:
  endpoints:
  - interval: {{ .Values.modelservice.metrics.serviceMonitor.interval }}
    port: {{ .Values.modelservice.metrics.serviceMonitor.port }}
    path: {{ .Values.modelservice.metrics.serviceMonitor.path }}
  jobLabel: {{ include "modelservice.fullname" . }}
  namespaceSelector:
    {{- if .Values.modelservice.metrics.serviceMonitor.namespaceSelector.any }}
    any: true
    {{- else }}
    matchNames:
    - {{ .Release.Namespace }}
    {{- if .Values.modelservice.metrics.serviceMonitor.namespaceSelector.matchNames }}
    {{- range .Values.modelservice.metrics.serviceMonitor.namespaceSelector.matchNames }}
    - {{ . }}
    {{- end }}
    {{- end }}
    {{- end }}
  selector:
    matchLabels:
      {{- if .Values.modelservice.metrics.serviceMonitor.selector.matchLabels }}
      {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.metrics.serviceMonitor.selector.matchLabels "context" $ ) | nindent 4 }}
      {{- end }}
      {{ include "metrics.label" . }}
{{- end }}
