{{- if .Values.modelservice.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: basic-gpu-with-nixl-and-redis-lookup-preset
  labels:
    {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: modelservice
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  annotations:
    {{- if .Values.commonAnnotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
    {{- end }}
    {{- if .Values.modelservice.annotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.annotations "context" $) | nindent 4 }}
    {{- end }}
data:
  decodeDeployment: |
    apiVersion: apps/v1
    kind: Deployment
    spec:
      template:
        spec:
          {{- if .Values.modelservice.decode.podSecurityContext }}
          securityContext:
            {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.decode.podSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.modelservice.decode.affinity }}
          affinity:
            {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.decode.affinity "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.modelservice.decode.topologySpreadConstraints }}
          topologySpreadConstraints:
            {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.decode.topologySpreadConstraints "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.modelservice.decode.nodeSelector }}
          nodeSelector:
            {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.decode.nodeSelector "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.modelservice.decode.tolerations }}
          tolerations:
            {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.decode.tolerations "context" $) | nindent 12 }}
          {{- end }}
          initContainers:
            - name: routing-proxy
              image: {{ include "modelservice.routingProxyImage" . }}
              {{- if .Values.modelservice.decode.vllm.containerSecurityContext }}
              securityContext:
                {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.decode.vllm.containerSecurityContext "context" $) | nindent 16 }}
              {{- end }}
              args:
                - "--port=8000"
                - "--vllm-port=8001"
                - "--connector=nixlv2"
              ports:
                - containerPort: 8000
                  protocol: TCP
              restartPolicy: Always
              imagePullPolicy: {{ .Values.modelservice.routingProxy.image.imagePullPolicy }}
              livenessProbe:
                tcpSocket:
                  port: 8000
                failureThreshold: 3
                periodSeconds: 5
              readinessProbe:
                tcpSocket:
                  port: 8000
                failureThreshold: 3
                periodSeconds: 5
          containers:
            - name: vllm
              image: {{ include "modelservice.vllmImage" . }}
              imagePullPolicy: {{ .Values.modelservice.vllm.image.imagePullPolicy }}
              securityContext:
                capabilities:
                  drop:
                    - MKNOD
                allowPrivilegeEscalation: false
              command:
                - vllm
                - serve
                - {{ `{{ default (print "/models/" .ModelPath) .HFModelName }}` }}
              args:
                - "--port"
                - "8001"
                - "--kv-transfer-config"
                - '{"kv_connector":"MultiConnector","kv_role":"kv_both","kv_connector_extra_config":{"connectors":[{"kv_connector":"NixlConnector","kv_role":"kv_both"},{"kv_connector":"LMCacheConnectorV1","kv_role":"kv_both"}]}}'
              env:
                - name: HOME
                  value: /home
                {{ if .Values.modelservice.vllm.logLevel }}
                - name: VLLM_LOGGING_LEVEL
                  value: {{ .Values.modelservice.vllm.logLevel }}
                {{- end }}
                - name: VLLM_NIXL_SIDE_CHANNEL_PORT
                  value: "5557"
                - name: VLLM_NIXL_SIDE_CHANNEL_HOST
                  valueFrom:
                    fieldRef:
                      fieldPath: status.podIP
                - name: LMCACHE_DISTRIBUTED_URL
                  valueFrom:
                    fieldRef:
                      fieldPath: status.podIP
                - name: UCX_TLS
                  value: "^cuda_ipc"
                - name: LMCACHE_ENABLE_DEBUG
                  value: "True"
                - name: LMCACHE_LOCAL_CPU
                  value: "True"
                - name: LMCACHE_MAX_LOCAL_CPU_SIZE
                  value: "5"
                - name: LMCACHE_MAX_LOCAL_DISK_SIZE
                  value: "10"
                - name: LMCACHE_CHUNK_SIZE
                  value: "256"
                - name: LMCACHE_LOOKUP_URL
                  value: {{ include "redis.master.service.fullurl" . }}
                {{- if ne .Values.redis.auth.enabled false }}
                - name: LMCACHE_REDIS_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: {{ .Values.redis.auth.existingSecret | default "llm-d-redis" }}
                      key: {{ .Values.redis.auth.existingSecretPasswordKey | default "redis-password"}}
                {{- end }}
                {{ `{{- if .HFModelName }}` }}
                - name: HF_HUB_CACHE
                  value: /models
                {{ `{{- end }}` }}
              startupProbe:
                httpGet:
                  path: /health
                  port: 8001
                failureThreshold: 60
                initialDelaySeconds: 15
                periodSeconds: 30
                timeoutSeconds: 5
              livenessProbe:
                tcpSocket:
                  port: 8001
                failureThreshold: 3
                periodSeconds: 5
              readinessProbe:
                httpGet:
                  path: /health
                  port: 8001
                failureThreshold: 3
                periodSeconds: 5
              volumeMounts:
                - name: home
                  mountPath: /home
                - name: dshm
                  mountPath: /dev/shm
                {{ `{{- if .HFModelName }}` }}
                - name: model-cache
                  mountPath: /models
                {{ `{{- else }}` }}
                - name: model-storage
                  mountPath: /models
                  readOnly: true
                {{ `{{- end }}` }}
              ports:
                - containerPort: 5557
                  protocol: TCP
                - containerPort: 80
                  protocol: TCP
          volumes:
            - name: home
              emptyDir: {}
            - name: dshm
              emptyDir:
                medium: Memory
                sizeLimit: 1Gi
            {{ `{{- if .HFModelName }}` }}
            - name: model-cache
              emptyDir: {}
            {{ `{{- end }}` }}

  prefillDeployment: |
    apiVersion: apps/v1
    kind: Deployment
    spec:
      template:
        spec:
          {{- if .Values.modelservice.prefill.podSecurityContext }}
          securityContext:
            {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.prefill.podSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.modelservice.prefill.affinity }}
          affinity:
            {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.prefill.affinity "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.modelservice.prefill.topologySpreadConstraints }}
          topologySpreadConstraints:
            {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.prefill.topologySpreadConstraints "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.modelservice.prefill.nodeSelector }}
          nodeSelector:
            {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.prefill.nodeSelector "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.modelservice.prefill.tolerations }}
          tolerations:
            {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.prefill.tolerations "context" $) | nindent 12 }}
          {{- end }}
          containers:
            - name: vllm
              image: {{ include "modelservice.vllmImage" . }}
              imagePullPolicy: {{ .Values.modelservice.vllm.image.imagePullPolicy }}
              {{- if .Values.modelservice.prefill.vllm.containerSecurityContext }}
              securityContext:
                {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.prefill.vllm.containerSecurityContext "context" $) | nindent 16 }}
              {{- end }}
              command:
                - vllm
                - serve
                - {{ `{{ default (print "/models/" .ModelPath) .HFModelName }}` }}
              args:
                - "--port"
                - "8000"
                - "--kv-transfer-config"
                - '{"kv_connector":"MultiConnector","kv_role":"kv_both","kv_connector_extra_config":{"connectors":[{"kv_connector":"NixlConnector","kv_role":"kv_both"},{"kv_connector":"LMCacheConnectorV1","kv_role":"kv_both"}]}}'
              env:
                - name: HOME
                  value: /home
                {{ if .Values.modelservice.vllm.logLevel }}
                - name: VLLM_LOGGING_LEVEL
                  value: {{ .Values.modelservice.vllm.logLevel }}
                {{- end }}
                - name: VLLM_NIXL_SIDE_CHANNEL_PORT
                  value: "5557"
                - name: VLLM_NIXL_SIDE_CHANNEL_HOST
                  valueFrom:
                    fieldRef:
                      fieldPath: status.podIP
                - name: LMCACHE_DISTRIBUTED_URL
                  valueFrom:
                    fieldRef:
                      fieldPath: status.podIP
                - name: UCX_TLS
                  value: "^cuda_ipc"
                - name: LMCACHE_ENABLE_DEBUG
                  value: "True"
                - name: LMCACHE_LOCAL_CPU
                  value: "True"
                - name: LMCACHE_MAX_LOCAL_CPU_SIZE
                  value: "5"
                - name: LMCACHE_MAX_LOCAL_DISK_SIZE
                  value: "10"
                - name: LMCACHE_CHUNK_SIZE
                  value: "256"
                - name: LMCACHE_LOOKUP_URL
                  value: {{ include "redis.master.service.fullurl" . }}
                {{- if ne .Values.redis.auth.enabled false }}
                - name: LMCACHE_REDIS_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: {{ .Values.redis.auth.existingSecret | default "llm-d-redis" }}
                      key: {{ .Values.redis.auth.existingSecretPasswordKey | default "redis-password"}}
                {{- end }}
                {{ `{{- if .HFModelName }}` }}
                - name: HF_HUB_CACHE
                  value: /models
                {{ `{{- end }}` }}
              startupProbe:
                httpGet:
                  path: /health
                  port: 8000
                failureThreshold: 60
                initialDelaySeconds: 15
                periodSeconds: 30
                timeoutSeconds: 5
              livenessProbe:
                tcpSocket:
                  port: 8000
                failureThreshold: 3
                periodSeconds: 5
              readinessProbe:
                httpGet:
                  path: /health
                  port: 8000
                failureThreshold: 3
                periodSeconds: 5
              volumeMounts:
                - name: home
                  mountPath: /home
                - name: dshm
                  mountPath: /dev/shm
                {{ `{{- if .HFModelName }}` }}
                - name: model-cache
                  mountPath: /models
                {{ `{{- else }}` }}
                - name: model-storage
                  mountPath: /models
                  readOnly: true
                {{ `{{- end }}` }}
              ports:
                - containerPort: 5557
                  protocol: TCP
                - containerPort: 80
                  protocol: TCP
          volumes:
            - name: home
              emptyDir: {}
            - name: dshm
              emptyDir:
                medium: Memory
                sizeLimit: 1Gi
            {{ `{{ if .HFModelName }}` }}
            - name: model-cache
              emptyDir: {}
            {{ `{{ end }}` }}

  decodeService: |
    apiVersion: v1
    kind: Service
    metadata:
      labels:
        {{- if .Values.modelservice.vllm.metrics.enabled }}
        {{ include "metrics.label" . }}
        {{- end }}
    spec:
      clusterIP: None
      ports:
      - name: nixl
        port: 5557
        protocol: TCP
      - name: vllm
        port: 8000
        protocol: TCP

  prefillService: |
    apiVersion: v1
    kind: Service
    metadata:
      labels:
        {{- if .Values.modelservice.vllm.metrics.enabled }}
        {{ include "metrics.label" . }}
        {{- end }}
    spec:
      clusterIP: None
      ports:
      - name: nixl
        port: 5557
        protocol: TCP
      - name: vllm
        port: 8000
        protocol: TCP

  eppService: |
    apiVersion: v1
    kind: Service
    metadata:
      labels:
        app.kubernetes.io/gateway: {{ include "gateway.fullname" . }}
        {{- if .Values.modelservice.epp.metrics.enabled }}
        {{ include "metrics.label" . }}
        {{- end}}
    spec:
      ports:
        - port: 9002
          protocol: TCP
          name: grpc
        - port: 9003
          protocol: TCP
          name: grpc-health
        - port: 9090
          protocol: TCP
          name: metrics
      type: NodePort
      selector:
        app.kubernetes.io/gateway: {{ include "gateway.fullname" . }}

  eppDeployment: |
    apiVersion: apps/v1
    kind: Deployment
    metadata:
      labels:
        app.kubernetes.io/gateway: {{ include "gateway.fullname" . }}
    spec:
      selector:
        matchLabels:
          app.kubernetes.io/gateway: {{ include "gateway.fullname" . }}
      template:
        metadata:
          labels:
            app.kubernetes.io/gateway: {{ include "gateway.fullname" . }}
        spec:
          {{- if .Values.modelservice.epp.podSecurityContext }}
          securityContext:
            {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.epp.podSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.modelservice.epp.affinity }}
          affinity:
            {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.epp.affinity "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.modelservice.epp.topologySpreadConstraints }}
          topologySpreadConstraints:
            {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.epp.topologySpreadConstraints "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.modelservice.epp.nodeSelector }}
          nodeSelector:
            {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.epp.nodeSelector "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.modelservice.epp.tolerations }}
          tolerations:
            {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.epp.tolerations "context" $) | nindent 12 }}
          {{- end }}
          containers:
            - args:
                - --poolName
                - {{`"{{ .InferencePoolName }}"`}}
                - --poolNamespace
                - {{`"{{ .ModelServiceNamespace }}"`}}
                - -v
                - "4"
                - --zap-encoder
                - json
                - --grpcPort
                - "9002"
                - --grpcHealthPort
                - "9003"
              env:
              {{- include "modelservice.epp.envList" . | nindent 14 }}
              {{/* HACK, waiting on: https://github.com/llm-d/llm-d-model-service/issues/123 */}}
              {{ `{{- if .HFModelName }}` }}
              - name: HF_TOKEN
                valueFrom:
                  secretKeyRef:
                    name: {{ .Values.sampleApplication.model.auth.hfToken.name }}
                    key: {{ .Values.sampleApplication.model.auth.hfToken.key }}
              {{ `{{- end }}` }}
              image: {{ include "modelservice.eppImage" . }}
              imagePullPolicy: {{ .Values.modelservice.epp.image.imagePullPolicy }}
              {{- if .Values.modelservice.epp.containerSecurityContext }}
              securityContext:
                {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.epp.containerSecurityContext "context" $) | nindent 16 }}
              {{- end }}
              resources:
                requests:
                  cpu: 256m
                  memory: 500Mi
              livenessProbe:
                failureThreshold: 3
                grpc:
                  port: 9003
                  service: "envoy.service.ext_proc.v3.ExternalProcessor"
                initialDelaySeconds: 5
                periodSeconds: 10
                successThreshold: 1
                timeoutSeconds: 1
              readinessProbe:
                failureThreshold: 3
                grpc:
                  port: 9003
                  service: "envoy.service.ext_proc.v3.ExternalProcessor"
                initialDelaySeconds: 5
                periodSeconds: 10
                successThreshold: 1
                timeoutSeconds: 1
              name: epp
              ports:
                - name: grpc
                  containerPort: 9002
                  protocol: TCP
                - name: grpc-health
                  containerPort: 9003
                  protocol: TCP
                - name: metrics
                  containerPort: 9090
                  protocol: TCP

  inferencePool: |
    apiVersion: inference.networking.x-k8s.io/v1alpha2
    kind: InferencePool
    spec:
      targetPortNumber: 8000

  inferenceModel: |
    apiVersion: inference.networking.x-k8s.io/v1alpha2
    kind: InferenceModel
{{- end }}
