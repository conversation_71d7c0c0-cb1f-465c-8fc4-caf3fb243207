{{- if .Values.modelservice.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: basic-sim-preset
  labels:
    {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: modelservice
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  annotations:
    {{- if .Values.commonAnnotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
    {{- end }}
    {{- if .Values.modelservice.annotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.annotations "context" $) | nindent 4 }}
    {{- end }}
data:
  decodeDeployment: |
    apiVersion: apps/v1
    kind: Deployment
    spec:
      template:
        spec:
          {{- if .Values.modelservice.decode.podSecurityContext }}
          securityContext:
            {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.decode.podSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.modelservice.decode.affinity }}
          affinity:
            {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.decode.affinity "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.modelservice.decode.topologySpreadConstraints }}
          topologySpreadConstraints:
            {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.decode.topologySpreadConstraints "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.modelservice.decode.nodeSelector }}
          nodeSelector:
            {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.decode.nodeSelector "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.modelservice.decode.tolerations }}
          tolerations:
            {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.decode.tolerations "context" $) | nindent 12 }}
          {{- end }}
          initContainers:
            - name: routing-proxy
              image: {{ include "modelservice.routingProxyImage" . }}
              {{- if .Values.modelservice.decode.vllm.containerSecurityContext }}
              securityContext:
                {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.decode.vllm.containerSecurityContext "context" $) | nindent 16 }}
              {{- end }}
              args:
                - "--port=8000"
                - "--vllm-port=8001"
              ports:
                - containerPort: 8000
                  protocol: TCP
              restartPolicy: Always
              imagePullPolicy: {{ .Values.modelservice.routingProxy.image.imagePullPolicy }}
          containers:
            - name: vllm
              image: {{ include "modelservice.inferenceSimulatorImage" . }}
              imagePullPolicy: {{ .Values.modelservice.inferenceSimulator.image.imagePullPolicy }}
              securityContext:
                capabilities:
                  drop:
                    - MKNOD
                allowPrivilegeEscalation: false
              args:
                - "--port"
                - "8001"
              env:
                - name: HOME
                  value: /home
              volumeMounts:
                - name: home
                  mountPath: /home
              ports:
                - containerPort: 5557
                  protocol: TCP
          volumes:
            - name: home
              emptyDir: {}

  prefillDeployment: |
    apiVersion: apps/v1
    kind: Deployment
    spec:
      template:
        spec:
          {{- if .Values.modelservice.prefill.podSecurityContext }}
          securityContext:
            {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.prefill.podSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.modelservice.prefill.affinity }}
          affinity:
            {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.prefill.affinity "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.modelservice.prefill.topologySpreadConstraints }}
          topologySpreadConstraints:
            {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.prefill.topologySpreadConstraints "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.modelservice.prefill.nodeSelector }}
          nodeSelector:
            {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.prefill.nodeSelector "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.modelservice.prefill.tolerations }}
          tolerations:
            {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.prefill.tolerations "context" $) | nindent 12 }}
          {{- end }}
          containers:
            - name: vllm
              image: {{ include "modelservice.inferenceSimulatorImage" . }}
              imagePullPolicy: {{ .Values.modelservice.inferenceSimulator.image.imagePullPolicy }}
              {{- if .Values.modelservice.prefill.vllm.containerSecurityContext }}
              securityContext:
                {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.prefill.vllm.containerSecurityContext "context" $) | nindent 16 }}
              {{- end }}
              args:
                - "--port"
                - "8000"
              env:
                - name: HOME
                  value: /home
              volumeMounts:
                - name: home
                  mountPath: /home
              ports:
                - containerPort: 5557
                  protocol: TCP
          volumes:
            - name: home
              emptyDir: {}

  decodeService: |
    apiVersion: v1
    kind: Service
    metadata:
      labels:
        {{- if .Values.modelservice.vllm.metrics.enabled }}
        {{ include "metrics.label" . }}
        {{- end }}
    spec:
      clusterIP: None
      ports:
      - name: vllm
        port: 8000
        protocol: TCP

  prefillService: |
    apiVersion: v1
    kind: Service
    metadata:
      labels:
        {{- if .Values.modelservice.vllm.metrics.enabled }}
        {{ include "metrics.label" . }}
        {{- end }}
    spec:
      clusterIP: None
      ports:
      - name: vllm
        port: 8000
        protocol: TCP

  eppService: |
    apiVersion: v1
    kind: Service
    metadata:
      labels:
        app.kubernetes.io/gateway: {{ include "gateway.fullname" . }}
        {{- if .Values.modelservice.epp.metrics.enabled }}
        {{ include "metrics.label" . }}
        {{- end}}
    spec:
      ports:
        - port: 9002
          protocol: TCP
          name: grpc
        - port: 9003
          protocol: TCP
          name: grpc-health
        - port: 9090
          protocol: TCP
          name: metrics
      type: NodePort
      selector:
        app.kubernetes.io/gateway: {{ include "gateway.fullname" . }}

  eppDeployment: |
    apiVersion: apps/v1
    kind: Deployment
    metadata:
      labels:
        app.kubernetes.io/gateway: {{ include "gateway.fullname" . }}
    spec:
      selector:
        matchLabels:
          app.kubernetes.io/gateway: {{ include "gateway.fullname" . }}
      template:
        metadata:
          labels:
            app.kubernetes.io/gateway: {{ include "gateway.fullname" . }}
        spec:
          {{- if .Values.modelservice.epp.podSecurityContext }}
          securityContext:
            {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.epp.podSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.modelservice.epp.affinity }}
          affinity:
            {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.epp.affinity "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.modelservice.epp.topologySpreadConstraints }}
          topologySpreadConstraints:
            {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.epp.topologySpreadConstraints "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.modelservice.epp.nodeSelector }}
          nodeSelector:
            {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.epp.nodeSelector "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.modelservice.epp.tolerations }}
          tolerations:
            {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.epp.tolerations "context" $) | nindent 12 }}
          {{- end }}
          containers:
            - args:
                - --poolName
                - {{`"{{ .InferencePoolName }}"`}}
                - --poolNamespace
                - {{`"{{ .ModelServiceNamespace }}"`}}
                - -v
                - "4"
                - --zap-encoder
                - json
                - --grpcPort
                - "9002"
                - --grpcHealthPort
                - "9003"
              env:
              {{- include "modelservice.epp.envList" . | nindent 14 }}
              image: {{ include "modelservice.eppImage" . }}
              imagePullPolicy: {{ .Values.modelservice.epp.image.imagePullPolicy }}
              {{- if .Values.modelservice.epp.containerSecurityContext }}
              securityContext:
                {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.epp.containerSecurityContext "context" $) | nindent 16 }}
              {{- end }}
              resources:
                requests:
                  cpu: 256m
                  memory: 128Mi
              livenessProbe:
                failureThreshold: 3
                grpc:
                  port: 9003
                  service: "envoy.service.ext_proc.v3.ExternalProcessor"
                initialDelaySeconds: 5
                periodSeconds: 10
                successThreshold: 1
                timeoutSeconds: 1
              readinessProbe:
                failureThreshold: 3
                grpc:
                  port: 9003
                  service: "envoy.service.ext_proc.v3.ExternalProcessor"
                initialDelaySeconds: 5
                periodSeconds: 10
                successThreshold: 1
                timeoutSeconds: 1
              name: epp
              ports:
                - name: grpc
                  containerPort: 9002
                  protocol: TCP
                - name: grpc-health
                  containerPort: 9003
                  protocol: TCP
                - name: metrics
                  containerPort: 9090
                  protocol: TCP

  inferencePool: |
    apiVersion: inference.networking.x-k8s.io/v1alpha2
    kind: InferencePool
    spec:
      targetPortNumber: 8000

  inferenceModel: |
    apiVersion: inference.networking.x-k8s.io/v1alpha2
    kind: InferenceModel
{{- end }}
