{{- if and .Values.modelservice.enabled .Values.modelservice.rbac.create }}
# Ref: https://github.com/llm-d/llm-d-model-service/blob/main/config/rbac/metrics_auth_role_binding.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "modelservice.fullname" . }}-metrics-auth
  labels:
    {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: modelservice
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  annotations:
    {{- if .Values.commonAnnotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
    {{- end }}
    {{- if .Values.modelservice.annotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.annotations "context" $) | nindent 4 }}
    {{- end }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "modelservice.fullname" . }}-metrics-auth
subjects:
- kind: ServiceAccount
  apiGroup: ""
  name: {{ include "modelservice.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
{{- end }}
