{{- if and .Values.modelservice.enabled .Values.modelservice.rbac.create }}
# Ref: https://github.com/llm-d/llm-d-model-service/blob/main/config/rbac/modelservice_editor_role.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "modelservice.fullname" . }}-editor
  labels:
    {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: modelservice
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  annotations:
    {{- if .Values.commonAnnotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
    {{- end }}
    {{- if .Values.modelservice.annotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.annotations "context" $) | nindent 4 }}
    {{- end }}
rules:
- apiGroups:
  - llm-d.ai
  resources:
  - modelservices
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - llm-d.ai
  resources:
  - modelservices/status
  verbs:
  - get
{{- end }}
