{{ if .Values.modelservice.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "modelservice.fullname" . }}
  labels: {{ include "common.labels.standard" . | nindent 4 }}
    control-plane: controller-manager
    app.kubernetes.io/component: modelservice
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
    {{- if .Values.modelservice.metrics.enabled }}
    {{ include "metrics.label" . }}
    {{- end }}
  annotations:
    {{- if .Values.commonAnnotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
    {{- end }}
    {{- if .Values.modelservice.annotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.modelservice.annotations "context" $) | nindent 4 }}
    {{- end }}
spec:
  ports:
  - port: {{ .Values.modelservice.service.port }}
    protocol: TCP
    targetPort: {{ .Values.modelservice.service.port }}
  selector: {{- include "common.labels.matchLabels" . | nindent 4 }}
    control-plane: controller-manager
    app.kubernetes.io/component: modelservice
  type: {{ .Values.modelservice.service.type }}
{{- end }}
