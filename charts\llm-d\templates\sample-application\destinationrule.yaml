{{- if and .Values.gateway.enabled .Values.sampleApplication.enabled (eq .Values.gateway.gatewayClassName "istio") }}
apiVersion: networking.istio.io/v1
kind: DestinationRule
metadata:
  name: {{ include "sampleApplication.sanitizedModelName" . }}-insecure-tls
spec:
  host: {{ include "sampleApplication.sanitizedModelName" . }}-epp-service
  trafficPolicy:
    tls:
      mode: SIMPLE
      insecureSkipVerify: true
{{- end }}
