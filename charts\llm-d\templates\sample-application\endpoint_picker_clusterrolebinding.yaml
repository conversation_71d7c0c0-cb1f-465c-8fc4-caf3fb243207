{{- if and .Values.modelservice.enabled .Values.sampleApplication.enabled }}
# Ref: https://github.com/llm-d/llm-d-model-service/blob/main/config/rbac/role_binding.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "sampleApplication.sanitizedModelName" . }}-endpoint-picker
  labels: {{ include "common.labels.standard" $ | nindent 4 }}
    app.kubernetes.io/component: sample-application
    {{- if $.Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" $.Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  annotations:
    {{- if $.Values.commonAnnotations }}
    {{- include "common.tplvalues.render" ( dict "value" $.Values.commonAnnotations "context" $ ) | nindent 4 }}
    {{- end }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "modelservice.fullname" . }}-epp-metrics-scrape
subjects:
- kind: ServiceAccount
  name: {{ include "sampleApplication.sanitizedModelName" . }}-epp-sa
  namespace: {{ .Release.Namespace }}
{{- end }}
