{{ if and .Values.gateway.enabled .Values.sampleApplication.enabled -}}
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: {{ include "sampleApplication.sanitizedModelName" . }}
  labels: {{ include "common.labels.standard" $ | nindent 4 }}
    app.kubernetes.io/component: sample-application
    {{- if $.Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" $.Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  annotations:
    {{- if $.Values.commonAnnotations }}
    {{- include "common.tplvalues.render" ( dict "value" $.Values.commonAnnotations "context" $ ) | nindent 4 }}
    {{- end }}
spec:
  parentRefs:
    - name: {{ include "gateway.fullname" . }}
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /
      backendRefs:
        - group: inference.networking.x-k8s.io
          kind: InferencePool
          name: "{{ include "sampleApplication.sanitizedModelName" . }}-inference-pool"
          port: {{ .Values.sampleApplication.inferencePoolPort }}
      {{- if not (contains "gke-l7" .Values.gateway.gatewayClassName) }}
      timeouts:
        backendRequest: "0s"
        request: "0s"
      {{- end }}
{{- end }}

{{- if and .Values.gateway.enabled .Values.sampleApplication.enabled (contains "gke-l7" .Values.gateway.gatewayClassName) }}
---
apiVersion: networking.gke.io/v1
kind: GCPBackendPolicy
metadata:
  name: {{ include "sampleApplication.sanitizedModelName" . }}-backend-policy
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels.standard" $ | nindent 4 }}
    app.kubernetes.io/component: sample-application
    {{- if $.Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" $.Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  annotations:
    {{- if $.Values.commonAnnotations }}
    {{- include "common.tplvalues.render" ( dict "value" $.Values.commonAnnotations "context" $ ) | nindent 4 }}
    {{- end }}
spec:
  default:
    logging:
      enabled: true
    timeoutSec: 300
  targetRef:
    group: inference.networking.x-k8s.io
    kind: InferencePool
    name: "{{ include "sampleApplication.sanitizedModelName" . }}-inference-pool"
---
apiVersion: networking.gke.io/v1
kind: HealthCheckPolicy
metadata:
  name: {{ include "sampleApplication.sanitizedModelName" . }}-health-check-policy
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels.standard" $ | nindent 4 }}
    app.kubernetes.io/component: sample-application
spec:
  targetRef:
    group: "inference.networking.x-k8s.io"
    kind: InferencePool
    name: "{{ include "sampleApplication.sanitizedModelName" . }}-inference-pool"
  default:
    config:
      type: HTTP
      httpHealthCheck:
          requestPath: /health
          port: {{ .Values.sampleApplication.inferencePoolPort }}
{{- end }}
