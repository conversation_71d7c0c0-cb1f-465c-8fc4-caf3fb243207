{{- if and .Values.gateway.enabled .Values.ingress.enabled (eq .Values.gateway.serviceType "NodePort") }}
{{- $gatewayFullname := include "gateway.fullname" . }}
{{- $host := include "sampleApplication.ingressHost" . }}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ $gatewayFullname }}
  labels: {{ include "common.labels.standard" $ | nindent 4 }}
    app.kubernetes.io/gateway: {{ $gatewayFullname }}
    app.kubernetes.io/component: sample-application
    {{- if $.Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" $.Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  annotations:
    {{- if $.Values.commonAnnotations }}
    {{- include "common.tplvalues.render" ( dict "value" $.Values.commonAnnotations "context" $ ) | nindent 4 }}
    {{- end }}
    {{- if .Values.ingress.annotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.ingress.annotations "context" $) | nindent 4 }}
    {{- end }}
spec:
  {{- if $.Values.ingress.ingressClassName }}
  ingressClassName: {{ $.Values.ingress.ingressClassName }}
  {{- end }}
  {{- if or .Values.ingress.tls.enabled .Values.ingress.extraTls }}
  tls:
    {{- if .Values.ingress.tls.enabled }}
    - hosts:
      - {{ $host }}
      secretName: {{ include "common.tplvalues.render" ( dict "value" .Values.ingress.tls.secretName "context" $ ) }}
    {{- end }}
    {{- if .Values.ingress.extraTls }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.ingress.extraTls "context" $ ) | nindent 4 }}
    {{- end }}
  {{- end }}
  rules:
    {{- range .Values.gateway.listeners }}
    - host: {{ $host }}
      http:
        paths:
          - path: {{ .path }}
            pathType: Prefix
            backend:
              service:
                name: {{ $gatewayFullname }}
                port:
                  number: {{ .port }}
    {{- end }}
    {{- range .Values.ingress.extraHosts }}
    - host: {{ .name | quote }}
      http:
        paths:
          - path: {{ default $.Values.ingress.path .path }}
            pathType: {{ default "Prefix" .pathType }}
            backend:
              service:
                name: {{ include "common.names.fullname" $ }}
                port:
                  number: {{ default $.Values.service.ports.backend .port }}
    {{- end }}
{{- end }}
