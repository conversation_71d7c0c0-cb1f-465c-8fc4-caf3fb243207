{{ if and .Values.gateway.enabled .Values.test.enabled -}}
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: {{ include "common.names.fullname" . }}-vllm-sim
  annotations:
    "helm.sh/hook": test
spec:
  parentRefs:
    - name: {{ include "gateway.fullname" . }}
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /
      backendRefs:
        - group: inference.networking.x-k8s.io
          kind: InferencePool
          name: "{{ include "common.names.fullname" . }}-vllm-sim-inference-pool"
          port: 8000
{{ end }}
