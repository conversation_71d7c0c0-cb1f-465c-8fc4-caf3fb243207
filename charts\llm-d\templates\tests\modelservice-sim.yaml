{{- if .Values.test.enabled -}}
apiVersion: llm-d.ai/v1alpha1
kind: ModelService
metadata:
  name: {{ include "common.names.fullname" . }}-vllm-sim
  annotations:
    "helm.sh/hook": test
spec:
  decoupleScaling: false
  baseConfigMapRef:
    name: basic-sim-preset
  routing:
    modelName: food-review
  modelArtifacts:
    uri: hf://stub
  decode:
    replicas: 1
    containers:
    - name: "vllm"
      args:
      - "--model"
      - food-review
  prefill:
    replicas: 1
    containers:
    - name: "vllm"
      args:
      - "--model"
      - food-review
{{- end }}
