{{- if .Values.test.enabled }}
apiVersion: v1
kind: Pod
metadata:
  name: {{ include "common.names.fullname" . }}-test-connection
  annotations:
    helm.sh/hook: test
    helm.sh/hook-weight: "1" # Deploy after other resources, implicit weight is 0
spec:
  restartPolicy: Never
  securityContext:
    seccompProfile:
      type: RuntimeDefault
  containers:
    - name: curl
      securityContext:
        allowPrivilegeEscalation: false
        readOnlyRootFilesystem: true
        capabilities:
          drop: ["ALL"]
      resources:
        requests:
          cpu: 10m
          memory: 20Mi
        limits:
          cpu: 10m
          memory: 20Mi
      livenessProbe:
        exec:
          command:
          - ls
          - /usr/bin/curl
      image: {{ include "common.images.image" (dict "imageRoot" .Values.test.image "global" .Values.global) }}
      imagePullPolicy: IfNotPresent
      command: ["/bin/sh", "-c"]
      args:
        - |
          echo -e "\e[32m🥷 Waiting for pods to come up\e[0m"
          echo ""
          curl --connect-timeout 5 --max-time 20 --retry 20 --retry-delay 10 --retry-max-time 60 --retry-all-errors http://{{ include "gateway.fullname" . }}-istio/v1/models

          echo ""
          echo ""
          echo -e  "\e[32m🥷 Basic chat validation\e[0m"
          echo ""
          curl --connect-timeout 5 --max-time 20 --retry 20 --retry-delay 10 --retry-max-time 60 --retry-all-errors http://{{ include "gateway.fullname" . }}-istio/v1/chat/completions \
            -H 'accept: application/json' \
            -H 'Content-Type: application/json' \
            -d '{"model":"food-review","messages":[{"content":"Say hi","role":"user"}],"stream":false}'
{{- end }}
