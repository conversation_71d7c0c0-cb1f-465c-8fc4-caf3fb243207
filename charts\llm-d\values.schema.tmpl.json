{"$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "properties": {"clusterDomain": {"default": "cluster.local", "description": "Default Kubernetes cluster domain", "required": [], "title": "clusterDomain"}, "common": {"additionalProperties": true, "description": "Parameters for bitnami.common dependency", "required": [], "title": "common"}, "commonAnnotations": {"additionalProperties": true, "description": "Annotations to add to all deployed objects", "required": [], "title": "commonAnnotations"}, "commonLabels": {"additionalProperties": true, "description": "Labels to add to all deployed objects", "required": [], "title": "commonLabels"}, "extraDeploy": {"description": "Array of extra objects to deploy with the release", "items": {"required": [], "type": ["string", "object"]}, "required": [], "title": "extraDeploy"}, "fullnameOverride": {"default": "", "description": "String to fully override common.names.fullname", "required": [], "title": "fullnameOverride"}, "gateway": {"additionalProperties": false, "default": "See below", "description": "Gateway configuration", "properties": {"annotations": {"additionalProperties": true, "description": "Additional annotations provided to the Gateway resource", "required": [], "title": "annotations"}, "enabled": {"default": "true", "description": "Deploy resources related to Gateway", "required": [], "title": "enabled"}, "fullnameOverride": {"default": "", "description": "String to fully override gateway.fullname", "required": [], "title": "fullnameOverride"}, "gatewayClassName": {"default": "istio", "description": "Gateway class that determines the backend used. Currently supported values: \"istio\", \"kgateway\", \"gke-l7-rilb\", or \"gke-l7-regional-external-managed\"", "required": [], "title": "gatewayClassName"}, "kGatewayParameters": {"additionalProperties": false, "description": "Special parameters applied to kGateway via GatewayParameters resource", "properties": {"proxyUID": {"default": false, "description": " type: [number, boolean] @schema", "required": [], "title": "proxyUID", "type": ["number", "boolean"]}}, "required": [], "title": "kGatewayParameters", "type": "object"}, "listeners": {"description": " items:  type: object  properties:    name:      description: Name is the name of the Listener. This name MUST be unique within a Gateway      type: string    path:      description: Path to expose via Ingress      type: string    port:      description: Port is the network port. Multiple listeners may use the same port, subject to the Listener compatibility rules      type: integer      minimum: 1      maximum: 65535    protocol:      description: Protocol specifies the network protocol this listener expects to receive      type: string @schema Set of listeners exposed via the Gateway, also propagated to the Ingress if enabled", "items": {"properties": {"name": {"description": "Name is the name of the Listener. This name MUST be unique within a Gateway", "required": [], "type": "string"}, "path": {"description": "Path to expose via Ingress", "required": [], "type": "string"}, "port": {"description": "Port is the network port. Multiple listeners may use the same port, subject to the Listener compatibility rules", "maximum": 65535, "minimum": 1, "required": [], "type": "integer"}, "protocol": {"description": "Protocol specifies the network protocol this listener expects to receive", "required": [], "type": "string"}}, "required": [], "type": "object"}, "required": [], "title": "listeners"}, "nameOverride": {"default": "", "description": "String to partially override gateway.fullname", "required": [], "title": "nameOverride"}, "serviceType": {"default": "NodePort", "description": "Gateway's service type. Ingress is only available if the service type is set to NodePort. Accepted values: [\"LoadBalancer\", \"NodePort\"]", "required": [], "title": "serviceType"}}, "required": [], "title": "gateway"}, "global": {"additionalProperties": false, "default": "See below", "description": "Global parameters Global Docker image parameters Please, note that this will override the image parameters, including dependencies, configured to use the global value Current available global Docker image parameters: imageRegistry, imagePullSecrets and storageClass", "properties": {"imagePullSecrets": {"description": "Global Docker registry secret names as an array </br> E.g. `imagePullSecrets: [myRegistryKeySecretName]`", "items": {"required": [], "type": "string"}, "required": [], "title": "imagePullSecrets"}, "imageRegistry": {"default": "", "description": "Global Docker image registry", "required": [], "title": "imageRegistry"}, "security": {"additionalProperties": false, "properties": {"allowInsecureImages": {"default": true, "required": [], "title": "allowInsecureImages", "type": "boolean"}}, "required": [], "title": "security", "type": "object"}}, "required": [], "title": "global"}, "ingress": {"additionalProperties": false, "default": "See below", "description": "Ingress configuration", "properties": {"annotations": {"additionalProperties": true, "description": "Additional annotations for the Ingress resource", "required": [], "title": "annotations"}, "clusterRouterBase": {"default": "", "description": "used as part of the host dirivation if not specified from OCP cluster domain (dont edit)", "required": [], "title": "clusterRouterBase"}, "enabled": {"default": "true", "description": "Deploy Ingress", "required": [], "title": "enabled"}, "extraHosts": {"description": "List of additional hostnames to be covered with this ingress record (e.g. a CNAME) <!-- E.g. extraHosts:   - name: llm-d.env.example.com     path: / (Optional)     pathType: Prefix (Optional)     port: 7007 (Optional) -->", "items": {"required": []}, "required": [], "title": "extraHosts"}, "extraTls": {"description": "The TLS configuration for additional hostnames to be covered with this ingress record. <br /> Ref: https://kubernetes.io/docs/concepts/services-networking/ingress/#tls <!-- E.g. extraTls:   - hosts:     - llm-d.env.example.com     secretName: llm-d-env -->", "items": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.networking.v1.IngressTLS", "required": []}, "required": [], "title": "extraTls"}, "host": {"default": "", "description": "Hostname to be used to expose the NodePort service to the inferencing gateway", "required": [], "title": "host"}, "ingressClassName": {"default": "", "description": "Name of the IngressClass cluster resource which defines which controller will implement the resource (e.g nginx)", "required": [], "title": "ingressClassName"}, "path": {"default": "/", "description": "Path to be used to expose the full route to access the inferencing gateway", "required": [], "title": "path"}, "tls": {"additionalProperties": false, "description": "Ingress TLS parameters", "properties": {"enabled": {"default": "false", "description": "Enable TLS configuration for the host defined at `ingress.host` parameter", "required": [], "title": "enabled"}, "secretName": {"default": "", "description": "The name to which the TLS Secret will be called", "required": [], "title": "secretName"}}, "required": [], "title": "tls"}}, "required": [], "title": "ingress"}, "kubeVersion": {"default": "", "description": "Override Kubernetes version", "required": [], "title": "kubeVersion"}, "modelservice": {"additionalProperties": false, "default": "See below", "description": "Model service controller configuration", "properties": {"affinity": {"additionalProperties": false, "description": "Affinity for pod assignment <br /> Ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity", "properties": {"nodeAffinity": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.NodeAffinity", "required": []}, "podAffinity": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.PodAffinity", "required": []}, "podAntiAffinity": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.PodAntiAffinity", "required": []}}, "required": [], "title": "affinity"}, "annotations": {"additionalProperties": true, "description": "Annotations to add to all modelservice resources", "required": [], "title": "annotations"}, "containerSecurityContext": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.SecurityContext", "description": "Security settings for a Container. <br /> Ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container", "required": []}, "decode": {"additionalProperties": false, "default": "See below", "description": "Decode options", "properties": {"affinity": {"additionalProperties": false, "description": "Affinity for pod assignment <br /> Ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity", "properties": {"nodeAffinity": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.NodeAffinity", "required": []}, "podAffinity": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.PodAffinity", "required": []}, "podAntiAffinity": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.PodAntiAffinity", "required": []}}, "required": [], "title": "affinity"}, "containerSecurityContext": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.SecurityContext", "description": "Security settings for a Container. <br /> Ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container", "required": []}, "nodeSelector": {"additionalProperties": true, "description": "Node labels for pod assignment <br /> Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#nodeselector", "required": [], "title": "nodeSelector"}, "podSecurityContext": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.SecurityContext", "description": "Security settings for a Pod.  The security settings that you specify for a Pod apply to all Containers in the Pod. <br /> Ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod", "required": []}, "tolerations": {"description": "Node tolerations for server scheduling to nodes with taints <br /> Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/taint-and-toleration/", "items": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.Toleration", "required": []}, "required": [], "title": "tolerations"}, "topologySpreadConstraints": {"description": "Topology Spread Constraints for pod assignment <br /> Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#pod-topology-spread-constraints", "items": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.TopologySpreadConstraint", "required": []}, "required": [], "title": "topologySpreadConstraints"}, "vllm": {"additionalProperties": false, "description": "vLLM container settings", "properties": {"containerSecurityContext": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.SecurityContext", "additionalProperties": true, "description": "Security settings for a Container. <br /> Ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container", "required": []}}, "required": [], "title": "vllm"}}, "required": [], "title": "decode"}, "enabled": {"default": "true", "description": "Toggle to deploy modelservice controller related resources", "required": [], "title": "enabled"}, "epp": {"additionalProperties": false, "default": "See below", "description": "Endpoint picker configuration", "properties": {"affinity": {"additionalProperties": false, "description": "Affinity for pod assignment <br /> Ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity", "properties": {"nodeAffinity": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.NodeAffinity", "required": []}, "podAffinity": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.PodAffinity", "required": []}, "podAntiAffinity": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.PodAntiAffinity", "required": []}}, "required": [], "title": "affinity"}, "containerSecurityContext": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.SecurityContext", "description": "Security settings for a Container. <br /> Ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container", "required": []}, "defaultEnvVars": {"description": "Default environment variables for endpoint picker, use `defaultEnvVarsOverride` to override default behavior by defining the same variable again. Ref: https://github.com/llm-d/llm-d-inference-scheduler/blob/main/docs/architecture.md#scorers--configuration", "items": {"anyOf": [{"additionalProperties": false, "properties": {"name": {"default": "ENABLE_KVCACHE_AWARE_SCORER", "required": [], "title": "name", "type": "string"}, "value": {"default": "false", "required": [], "title": "value", "type": "string"}}, "required": [], "type": "object"}, {"additionalProperties": false, "properties": {"name": {"default": "KVCACHE_AWARE_SCORER_WEIGHT", "required": [], "title": "name", "type": "string"}, "value": {"default": "1", "required": [], "title": "value", "type": "string"}}, "required": [], "type": "object"}, {"additionalProperties": false, "properties": {"name": {"default": "KVCACHE_INDEXER_REDIS_ADDR", "required": [], "title": "name", "type": "string"}, "value": {"default": "{{ if .Values.redis.enabled }}{{ include \"redis.master.service.fullurl\" . }}{{ end }}", "required": [], "title": "value", "type": "string"}}, "required": [], "type": "object"}, {"additionalProperties": false, "properties": {"name": {"default": "ENABLE_PREFIX_AWARE_SCORER", "required": [], "title": "name", "type": "string"}, "value": {"default": "true", "required": [], "title": "value", "type": "string"}}, "required": [], "type": "object"}, {"additionalProperties": false, "properties": {"name": {"default": "PREFIX_AWARE_SCORER_WEIGHT", "required": [], "title": "name", "type": "string"}, "value": {"default": "2", "required": [], "title": "value", "type": "string"}}, "required": [], "type": "object"}, {"additionalProperties": false, "properties": {"name": {"default": "ENABLE_LOAD_AWARE_SCORER", "required": [], "title": "name", "type": "string"}, "value": {"default": "true", "required": [], "title": "value", "type": "string"}}, "required": [], "type": "object"}, {"additionalProperties": false, "properties": {"name": {"default": "LOAD_AWARE_SCORER_WEIGHT", "required": [], "title": "name", "type": "string"}, "value": {"default": "1", "required": [], "title": "value", "type": "string"}}, "required": [], "type": "object"}, {"additionalProperties": false, "properties": {"name": {"default": "ENABLE_SESSION_AWARE_SCORER", "required": [], "title": "name", "type": "string"}, "value": {"default": "false", "required": [], "title": "value", "type": "string"}}, "required": [], "type": "object"}, {"additionalProperties": false, "properties": {"name": {"default": "SESSION_AWARE_SCORER_WEIGHT", "required": [], "title": "name", "type": "string"}, "value": {"default": "1", "required": [], "title": "value", "type": "string"}}, "required": [], "type": "object"}, {"additionalProperties": false, "properties": {"name": {"default": "PD_ENABLED", "required": [], "title": "name", "type": "string"}, "value": {"default": "false", "required": [], "title": "value", "type": "string"}}, "required": [], "type": "object"}, {"additionalProperties": false, "properties": {"name": {"default": "PD_PROMPT_LEN_THRESHOLD", "required": [], "title": "name", "type": "string"}, "value": {"default": "10", "required": [], "title": "value", "type": "string"}}, "required": [], "type": "object"}, {"additionalProperties": false, "properties": {"name": {"default": "PREFILL_ENABLE_KVCACHE_AWARE_SCORER", "required": [], "title": "name", "type": "string"}, "value": {"default": "false", "required": [], "title": "value", "type": "string"}}, "required": [], "type": "object"}, {"additionalProperties": false, "properties": {"name": {"default": "PREFILL_KVCACHE_AWARE_SCORER_WEIGHT", "required": [], "title": "name", "type": "string"}, "value": {"default": "1", "required": [], "title": "value", "type": "string"}}, "required": [], "type": "object"}, {"additionalProperties": false, "properties": {"name": {"default": "PREFILL_KVCACHE_INDEXER_REDIS_ADDR", "required": [], "title": "name", "type": "string"}, "value": {"default": "{{ if .Values.redis.enabled }}{{ include \"redis.master.service.fullurl\" . }}{{ end }}", "required": [], "title": "value", "type": "string"}}, "required": [], "type": "object"}, {"additionalProperties": false, "properties": {"name": {"default": "PREFILL_ENABLE_LOAD_AWARE_SCORER", "required": [], "title": "name", "type": "string"}, "value": {"default": "false", "required": [], "title": "value", "type": "string"}}, "required": [], "type": "object"}, {"additionalProperties": false, "properties": {"name": {"default": "PREFILL_LOAD_AWARE_SCORER_WEIGHT", "required": [], "title": "name", "type": "string"}, "value": {"default": "1", "required": [], "title": "value", "type": "string"}}, "required": [], "type": "object"}, {"additionalProperties": false, "properties": {"name": {"default": "PREFILL_ENABLE_PREFIX_AWARE_SCORER", "required": [], "title": "name", "type": "string"}, "value": {"default": "false", "required": [], "title": "value", "type": "string"}}, "required": [], "type": "object"}, {"additionalProperties": false, "properties": {"name": {"default": "PREFILL_PREFIX_AWARE_SCORER_WEIGHT", "required": [], "title": "name", "type": "string"}, "value": {"default": "1", "required": [], "title": "value", "type": "string"}}, "required": [], "type": "object"}, {"additionalProperties": false, "properties": {"name": {"default": "PREFILL_ENABLE_SESSION_AWARE_SCORER", "required": [], "title": "name", "type": "string"}, "value": {"default": "false", "required": [], "title": "value", "type": "string"}}, "required": [], "type": "object"}, {"additionalProperties": false, "properties": {"name": {"default": "PREFILL_SESSION_AWARE_SCORER_WEIGHT", "required": [], "title": "name", "type": "string"}, "value": {"default": "1", "required": [], "title": "value", "type": "string"}}, "required": [], "type": "object"}], "required": []}, "required": [], "title": "defaultEnvVars"}, "defaultEnvVarsOverride": {"description": "Override default environment variables for endpoint picker. This list has priorito over `defaultEnvVars`", "items": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.EnvVar", "required": []}, "required": [], "title": "defaultEnvVarsOverride"}, "image": {"additionalProperties": false, "default": "See below", "description": "Endpoint picker image used in ModelService CR presets", "properties": {"imagePullPolicy": {"default": "Always", "description": "Specify a imagePullPolicy", "required": [], "title": "imagePullPolicy"}, "pullSecrets": {"description": "Optionally specify an array of imagePullSecrets (evaluated as templates)", "items": {"required": [], "type": "string"}, "required": [], "title": "pullSecrets"}, "registry": {"default": "ghcr.io", "description": "Endpoint picker image registry", "required": [], "title": "registry"}, "repository": {"default": "llm-d/llm-d-inference-scheduler", "description": "Endpoint picker image repository", "required": [], "title": "repository"}, "tag": {"default": "0.0.4", "description": "Endpoint picker image tag", "required": [], "title": "tag"}}, "required": [], "title": "image"}, "metrics": {"additionalProperties": false, "description": "Enable metrics gathering via podMonitor / ServiceMonitor", "properties": {"enabled": {"default": "true", "description": "Enable metrics scraping from endpoint picker service", "required": [], "title": "enabled"}, "serviceMonitor": {"additionalProperties": false, "default": "See below", "description": "Prometheus ServiceMonitor configuration <br /> Ref: https://github.com/prometheus-operator/prometheus-operator/blob/main/Documentation/api-reference/api.md", "properties": {"annotations": {"additionalProperties": true, "description": "Additional annotations provided to the ServiceMonitor", "required": [], "title": "annotations"}, "interval": {"default": "10s", "description": "ServiceMonitor endpoint interval at which metrics should be scraped", "required": [], "title": "interval"}, "labels": {"additionalProperties": true, "description": "Additional labels provided to the ServiceMonitor", "required": [], "title": "labels"}, "namespaceSelector": {"additionalProperties": false, "description": "ServiceMonitor namespace selector", "properties": {"any": {"default": false, "required": [], "title": "any", "type": "boolean"}, "matchNames": {"description": " items:   type: string @schema", "items": {"required": [], "type": "string"}, "required": [], "title": "matchNames"}}, "required": [], "title": "namespaceSelector"}, "path": {"default": "/metrics", "description": "ServiceMonitor endpoint path", "required": [], "title": "path"}, "port": {"default": "metrics", "description": "ServiceMonitor endpoint port", "required": [], "title": "port"}, "selector": {"additionalProperties": false, "description": "ServiceMonitor selector matchLabels </br> matchLabels must match labels on modelservice Services", "properties": {"matchLabels": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.apimachinery.pkg.apis.meta.v1.LabelSelector", "description": " $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.apimachinery.pkg.apis.meta.v1.LabelSelector @schema", "required": []}}, "required": [], "title": "selector"}}, "required": [], "title": "serviceMonitor"}}, "required": [], "title": "metrics"}, "nodeSelector": {"additionalProperties": true, "description": "Node labels for pod assignment <br /> Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#nodeselector", "required": [], "title": "nodeSelector"}, "podSecurityContext": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.SecurityContext", "description": "Security settings for a Pod.  The security settings that you specify for a Pod apply to all Containers in the Pod. <br /> Ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod", "required": []}, "tolerations": {"description": "Node tolerations for server scheduling to nodes with taints <br /> Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/taint-and-toleration/", "items": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.Toleration", "required": []}, "required": [], "title": "tolerations"}, "topologySpreadConstraints": {"description": "Topology Spread Constraints for pod assignment <br /> Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#pod-topology-spread-constraints", "items": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.TopologySpreadConstraint", "required": []}, "required": [], "title": "topologySpreadConstraints"}}, "required": [], "title": "epp"}, "fullnameOverride": {"default": "", "description": "String to fully override modelservice.fullname", "required": [], "title": "fullnameOverride"}, "image": {"additionalProperties": false, "default": "See below", "description": "Modelservice controller image, please change only if appropriate adjustments to the CRD are being made", "properties": {"imagePullPolicy": {"default": "Always", "description": "Specify a imagePullPolicy", "required": [], "title": "imagePullPolicy"}, "pullSecrets": {"description": "Optionally specify an array of imagePullSecrets (evaluated as templates)", "items": {"required": [], "type": "string"}, "required": [], "title": "pullSecrets"}, "registry": {"default": "ghcr.io", "description": "Model Service controller image registry", "required": [], "title": "registry"}, "repository": {"default": "llm-d/llm-d-model-service", "description": "Model Service controller image repository", "required": [], "title": "repository"}, "tag": {"default": "0.0.10", "description": "Model Service controller image tag", "required": [], "title": "tag"}}, "required": [], "title": "image"}, "inferenceSimulator": {"additionalProperties": false, "default": "See below", "description": "llm-d inference simulator container options", "properties": {"containerSecurityContext": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.SecurityContext", "description": "Security settings for a Container. <br /> Ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container", "required": []}, "image": {"additionalProperties": false, "default": "See below", "description": "llm-d inference simulator image used in ModelService CR presets", "properties": {"imagePullPolicy": {"default": "IfNotPresent", "description": "Specify a imagePullPolicy", "required": [], "title": "imagePullPolicy"}, "pullSecrets": {"description": "Optionally specify an array of imagePullSecrets (evaluated as templates)", "items": {"required": [], "type": "string"}, "required": [], "title": "pullSecrets"}, "registry": {"default": "ghcr.io", "description": "llm-d inference simulator image registry", "required": [], "title": "registry"}, "repository": {"default": "llm-d/llm-d-inference-sim", "description": "llm-d inference simulator image repository", "required": [], "title": "repository"}, "tag": {"default": "0.0.4", "description": "llm-d inference simulator image tag", "required": [], "title": "tag"}}, "required": [], "title": "image"}}, "required": [], "title": "inferenceSimulator"}, "metrics": {"additionalProperties": false, "description": "Enable metrics gathering via podMonitor / ServiceMonitor", "properties": {"enabled": {"default": "true", "description": "Enable metrics scraping from prefill and decode services, see `model", "required": [], "title": "enabled"}, "serviceMonitor": {"additionalProperties": false, "default": "See below", "description": "Prometheus ServiceMonitor configuration <br /> Ref: https://github.com/prometheus-operator/prometheus-operator/blob/main/Documentation/api-reference/api.md", "properties": {"annotations": {"additionalProperties": true, "description": "Additional annotations provided to the ServiceMonitor", "required": [], "title": "annotations"}, "interval": {"default": "15s", "description": "ServiceMonitor endpoint interval at which metrics should be scraped", "required": [], "title": "interval"}, "labels": {"additionalProperties": true, "description": "Additional labels provided to the ServiceMonitor", "required": [], "title": "labels"}, "namespaceSelector": {"additionalProperties": false, "description": "ServiceMonitor namespace selector", "properties": {"any": {"default": false, "required": [], "title": "any", "type": "boolean"}, "matchNames": {"description": " items:   type: string @schema", "items": {"required": [], "type": "string"}, "required": [], "title": "matchNames"}}, "required": [], "title": "namespaceSelector"}, "path": {"default": "/metrics", "description": "ServiceMonitor endpoint path", "required": [], "title": "path"}, "port": {"default": "vllm", "description": "ServiceMonitor endpoint port", "required": [], "title": "port"}, "selector": {"additionalProperties": false, "description": "ServiceMonitor selector matchLabels </br> matchLabels must match labels on modelservice Services", "properties": {"matchLabels": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.apimachinery.pkg.apis.meta.v1.LabelSelector", "description": " $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.apimachinery.pkg.apis.meta.v1.LabelSelector @schema", "required": []}}, "required": [], "title": "selector"}}, "required": [], "title": "serviceMonitor"}}, "required": [], "title": "metrics"}, "nameOverride": {"default": "", "description": "String to partially override modelservice.fullname", "required": [], "title": "nameOverride"}, "nodeSelector": {"additionalProperties": true, "description": "Node labels for pod assignment <br /> Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#nodeselector", "required": [], "title": "nodeSelector"}, "podAnnotations": {"additionalProperties": true, "description": "Pod annotations for modelservice", "required": [], "title": "podAnnotations"}, "podLabels": {"additionalProperties": true, "description": "Pod labels for modelservice", "required": [], "title": "podLabels"}, "podSecurityContext": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.SecurityContext", "description": "Security settings for a Pod.  The security settings that you specify for a Pod apply to all Containers in the Pod. <br /> Ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod", "required": []}, "prefill": {"additionalProperties": false, "default": "See below", "description": "Prefill options", "properties": {"affinity": {"additionalProperties": false, "description": "Affinity for pod assignment <br /> Ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity", "properties": {"nodeAffinity": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.NodeAffinity", "required": []}, "podAffinity": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.PodAffinity", "required": []}, "podAntiAffinity": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.PodAntiAffinity", "required": []}}, "required": [], "title": "affinity"}, "containerSecurityContext": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.SecurityContext", "description": "Security settings for a Container. <br /> Ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container", "required": []}, "nodeSelector": {"additionalProperties": true, "description": "Node labels for pod assignment <br /> Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#nodeselector", "required": [], "title": "nodeSelector"}, "podSecurityContext": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.SecurityContext", "description": "Security settings for a Pod.  The security settings that you specify for a Pod apply to all Containers in the Pod. <br /> Ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod", "required": []}, "tolerations": {"description": "Node tolerations for server scheduling to nodes with taints <br /> Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/taint-and-toleration/", "items": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.Toleration", "required": []}, "required": [], "title": "tolerations"}, "topologySpreadConstraints": {"description": "Topology Spread Constraints for pod assignment <br /> Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#pod-topology-spread-constraints", "items": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.TopologySpreadConstraint", "required": []}, "required": [], "title": "topologySpreadConstraints"}, "vllm": {"additionalProperties": false, "description": "vLLM container settings", "properties": {"containerSecurityContext": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.SecurityContext", "additionalProperties": true, "description": "Security settings for a Container. <br /> Ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container", "required": []}}, "required": [], "title": "vllm"}}, "required": [], "title": "prefill"}, "rbac": {"additionalProperties": false, "properties": {"create": {"default": "true", "description": "Enable the creation of RBAC resources", "required": [], "title": "create"}}, "required": [], "title": "rbac", "type": "object"}, "replicas": {"default": "1", "description": "Number of controller replicas", "required": [], "title": "replicas"}, "routingProxy": {"additionalProperties": false, "default": "See below", "description": "Routing proxy container options", "properties": {"containerSecurityContext": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.SecurityContext", "description": "Security settings for a Container. <br /> Ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container", "required": []}, "image": {"additionalProperties": false, "description": "Routing proxy image used in ModelService CR presets", "properties": {"imagePullPolicy": {"default": "IfNotPresent", "description": "Specify a imagePullPolicy", "required": [], "title": "imagePullPolicy"}, "pullSecrets": {"description": "Optionally specify an array of imagePullSecrets (evaluated as templates)", "items": {"required": [], "type": "string"}, "required": [], "title": "pullSecrets"}, "registry": {"default": "ghcr.io", "description": "Routing proxy image registry", "required": [], "title": "registry"}, "repository": {"default": "llm-d/llm-d-routing-sidecar", "description": "Routing proxy image repository", "required": [], "title": "repository"}, "tag": {"default": "0.0.6", "description": "Routing proxy image tag", "required": [], "title": "tag"}}, "required": [], "title": "image"}}, "required": [], "title": "routingProxy"}, "service": {"additionalProperties": false, "description": "Model service controller settings", "properties": {"enabled": {"default": "true", "description": "Toggle to deploy a Service resource for Model service controller", "required": [], "title": "enabled"}, "port": {"default": "8443", "description": "Port number exposed from Model Service controller", "required": [], "title": "port"}, "type": {"default": "ClusterIP", "description": "Service type", "required": [], "title": "type"}}, "required": [], "title": "service", "type": "object"}, "serviceAccount": {"additionalProperties": false, "default": "See below", "description": "Service Account Configuration", "properties": {"annotations": {"additionalProperties": true, "description": "Additional custom annotations for the ServiceAccount.", "required": [], "title": "annotations"}, "create": {"default": "true", "description": "Enable the creation of a ServiceAccount for Modelservice pods", "required": [], "title": "create"}, "fullnameOverride": {"default": "", "description": "String to fully override modelservice.serviceAccountName, defaults to modelservice.fullname", "required": [], "title": "fullnameOverride"}, "labels": {"additionalProperties": true, "description": "Additional custom labels to the service ServiceAccount.", "required": [], "title": "labels"}, "nameOverride": {"default": "", "description": "String to partially override modelservice.serviceAccountName, defaults to modelservice.fullname", "required": [], "title": "nameOverride"}}, "required": [], "title": "serviceAccount"}, "tolerations": {"description": "Node tolerations for server scheduling to nodes with taints <br /> Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/taint-and-toleration/", "items": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.Toleration", "required": []}, "required": [], "title": "tolerations"}, "topologySpreadConstraints": {"description": "Topology Spread Constraints for pod assignment <br /> Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#pod-topology-spread-constraints", "items": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.TopologySpreadConstraint", "required": []}, "required": [], "title": "topologySpreadConstraints"}, "vllm": {"additionalProperties": false, "default": "See below", "description": "vLLM container options", "properties": {"image": {"additionalProperties": false, "default": "See below", "description": "vLLM image used in ModelService CR presets", "properties": {"imagePullPolicy": {"default": "IfNotPresent", "description": "Specify a imagePullPolicy", "required": [], "title": "imagePullPolicy"}, "pullSecrets": {"description": "Optionally specify an array of imagePullSecrets (evaluated as templates)", "items": {"required": [], "type": "string"}, "required": [], "title": "pullSecrets"}, "registry": {"default": "ghcr.io", "description": "llm-d image registry", "required": [], "title": "registry"}, "repository": {"default": "llm-d/llm-d", "description": "llm-d image repository", "required": [], "title": "repository"}, "tag": {"default": "0.0.8", "description": "llm-d image tag", "required": [], "title": "tag"}}, "required": [], "title": "image"}, "logLevel": {"default": "INFO", "description": "Log level to run VLLM with <br /> VLLM supports standard python log-levels, see: https://docs.python.org/3/library/logging.html#logging-levels <br /> Options: \"DEBUG\", \"INFO\", \"WARNING\", \"ERROR\", \"CRITICAL\"", "required": [], "title": "logLevel"}, "metrics": {"additionalProperties": false, "description": "Enable metrics gathering via podMonitor / ServiceMonitor", "properties": {"enabled": {"default": "true", "description": "Enable metrics scraping from prefill & decode services", "required": [], "title": "enabled"}}, "required": [], "title": "metrics"}}, "required": [], "title": "vllm"}}, "required": [], "title": "modelservice"}, "nameOverride": {"default": "", "description": "String to partially override common.names.fullname", "required": [], "title": "nameOverride"}, "redis": {"$ref": "https://raw.githubusercontent.com/bitnami/charts/refs/tags/redis/20.13.4/bitnami/redis/values.schema.json", "default": "Use sane defaults for minimal Redis deployment", "description": "Bitnami/Redis chart configuration", "required": []}, "sampleApplication": {"additionalProperties": false, "default": "See below", "description": "Sample application deploying a p-d pair of specific model", "properties": {"baseConfigMapRefName": {"default": "basic-gpu-with-nixl-and-redis-lookup-preset", "description": "Name of the base configMapRef to use <br /> For the available presets see: `templates/modelservice/presets/`", "required": [], "title": "baseConfigMapRefName"}, "decode": {"additionalProperties": false, "properties": {"env": {"description": "environment variables injected into each decode vLLM container", "items": {"required": []}, "required": [], "title": "env"}, "extraArgs": {"description": "args to add to the decode deployment", "items": {"required": [], "type": "string"}, "required": [], "title": "extraArgs"}, "replicas": {"default": "1", "description": "number of desired decode replicas", "required": [], "title": "replicas"}}, "required": [], "title": "decode", "type": "object"}, "enabled": {"default": "true", "description": "Enable rendering of sample application resources", "required": [], "title": "enabled"}, "endpointPicker": {"additionalProperties": false, "properties": {"env": {"description": "Apply additional env variables to the endpoint picker deployment <br /> Ref: https://github.com/neuralmagic/llm-d-inference-scheduler/blob/0.0.2/docs/architecture.md", "items": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.EnvVar", "required": []}, "required": [], "title": "env"}}, "required": [], "title": "endpointPicker", "type": "object"}, "inferencePoolPort": {"default": "8000", "description": "InferencePool port configuration", "required": [], "title": "inferencePoolPort"}, "model": {"additionalProperties": false, "properties": {"auth": {"additionalProperties": false, "properties": {"hfToken": {"additionalProperties": false, "description": "HF token auth config via k8s secret.", "properties": {"key": {"default": "HF_TOKEN", "description": "Key within the secret under which the token is located", "required": [], "title": "key"}, "name": {"default": "llm-d-hf-token", "description": "Name of the secret to create to store your huggingface token", "required": [], "title": "name"}}, "required": [], "title": "hfToken"}}, "required": [], "title": "auth", "type": "object"}, "modelArtifactURI": {"default": "hf://meta-llama/Llama-3.2-3B-Instruct", "description": "Fully qualified model artifact location URI <br /> For Hugging Face models use: `hf://<organization>/<repo>` <br /> For models located on PVC use: `pvc://<pvc_name>/<path_to_model>`", "required": [], "title": "modelArtifactURI"}, "modelName": {"default": "meta-llama/Llama-3.2-3B-Instruct", "description": "Name of the model", "required": [], "title": "modelName"}, "servedModelNames": {"description": "Aliases to the Model named vllm will serve with", "items": {"required": []}, "required": [], "title": "servedModelNames"}}, "required": [], "title": "model", "type": "object"}, "prefill": {"additionalProperties": false, "properties": {"env": {"description": "environment variables injected into each decode vLLM container", "items": {"required": []}, "required": [], "title": "env"}, "extraArgs": {"description": "args to add to the prefill deployment", "items": {"required": [], "type": "string"}, "required": [], "title": "extraArgs"}, "replicas": {"default": "1", "description": "number of desired prefill replicas", "required": [], "title": "replicas"}}, "required": [], "title": "prefill", "type": "object"}, "resources": {"$ref": "https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.ResourceRequirements", "description": "Resource requests/limits <br /> Ref: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/#resource-requests-and-limits-of-pod-and-container", "required": []}}, "required": [], "title": "sampleApplication"}, "test": {"additionalProperties": false, "description": "Helm tests", "properties": {"enabled": {"default": "false", "description": "Enable rendering of helm test resources", "required": [], "title": "enabled"}, "image": {"additionalProperties": false, "description": "See below", "properties": {"imagePullPolicy": {"default": "Always", "description": "Specify a imagePullPolicy", "required": [], "title": "imagePullPolicy"}, "pullSecrets": {"description": "Optionally specify an array of imagePullSecrets (evaluated as templates)", "items": {"required": [], "type": "string"}, "required": [], "title": "pullSecrets"}, "registry": {"default": "quay.io", "description": "Test connection pod image registry", "required": [], "title": "registry"}, "repository": {"default": "curl/curl", "description": "Test connection pod image repository. Note that the image needs to have both the `sh` and `curl` binaries in it.", "required": [], "title": "repository"}, "tag": {"default": "latest", "description": "Test connection pod image tag. Note that the image needs to have both the `sh` and `curl` binaries in it.", "required": [], "title": "tag"}}, "required": [], "title": "image"}}, "required": [], "title": "test"}}, "required": [], "type": "object"}