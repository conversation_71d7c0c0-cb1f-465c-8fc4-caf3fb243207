# yaml-language-server: $schema=values.schema.json

# Default values for the llm-d chart.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

# -- Global parameters
# Global Docker image parameters
# Please, note that this will override the image parameters, including dependencies, configured to use the global value
# Current available global Docker image parameters: imageRegistry, imagePullSecrets and storageClass
# @default -- See below
global:
  # -- Global Docker image registry
  imageRegistry: ""

  # @schema
  # items:
  #   type: string
  # @schema
  # -- Global Docker registry secret names as an array
  # </br> E.g. `imagePullSecrets: [myRegistryKeySecretName]`
  imagePullSecrets: []

  security:
    allowInsecureImages: true


# @schema
# additionalProperties: true
# @schema
# -- Parameters for bitnami.common dependency
common: {}

# -- Common parameters
# -- Override Kubernetes version
kubeVersion: ""

# -- String to partially override common.names.fullname
nameOverride: ""

# -- String to fully override common.names.fullname
fullnameOverride: ""

# -- Default Kubernetes cluster domain
clusterDomain: cluster.local

# @schema
# additionalProperties: true
# @schema
# -- Labels to add to all deployed objects
commonLabels: {}

# @schema
# additionalProperties: true
# @schema
# -- Annotations to add to all deployed objects
commonAnnotations: {}

# @schema
# items:
#   type: [string, object]
# @schema
# -- Array of extra objects to deploy with the release
extraDeploy: []

# -- Helm tests
test:

  # -- Enable rendering of helm test resources
  enabled: false

  # @default -- See below
  image:

    # -- Test connection pod image registry
    registry: quay.io

    # -- Test connection pod image repository. Note that the image needs to have both the `sh` and `curl` binaries in it.
    repository: curl/curl

    # -- Test connection pod image tag. Note that the image needs to have both the `sh` and `curl` binaries in it.
    tag: latest

    # -- Specify a imagePullPolicy
    imagePullPolicy: "Always"

    # @schema
    # items:
    #   type: string
    # @schema
    # -- Optionally specify an array of imagePullSecrets (evaluated as templates)
    pullSecrets: []

# -- Sample application deploying a p-d pair of specific model
# @default -- See below
sampleApplication:

  # -- Enable rendering of sample application resources
  enabled: true

  # -- Name of the base configMapRef to use
  # <br /> For the available presets see: `templates/modelservice/presets/`
  baseConfigMapRefName: basic-gpu-with-nixl-and-redis-lookup-preset

  model:
    # -- Fully qualified model artifact location URI
    # <br /> For Hugging Face models use: `hf://<organization>/<repo>`
    # <br /> For models located on PVC use: `pvc://<pvc_name>/<path_to_model>`
    modelArtifactURI: hf://meta-llama/Llama-3.2-3B-Instruct

    # -- Name of the model
    modelName: "meta-llama/Llama-3.2-3B-Instruct"

    # -- Aliases to the Model named vllm will serve with
    servedModelNames: []

    auth:

      # -- HF token auth config via k8s secret.
      hfToken:

        # -- Name of the secret to create to store your huggingface token
        name: llm-d-hf-token

        # -- Key within the secret under which the token is located
        key: HF_TOKEN

  # @schema
  # $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.ResourceRequirements
  # @schema
  # -- Modify resource limits/requests available to the pods
  # -- Resource requests/limits
  # <br /> Ref: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/#resource-requests-and-limits-of-pod-and-container
  resources:
    limits:
      nvidia.com/gpu: "1"
    requests:
      nvidia.com/gpu: "1"

  # -- InferencePool port configuration
  inferencePoolPort: 8000

  prefill:
    # -- number of desired prefill replicas
    replicas: 1

    # @schema
    # items:
    #   type: string
    # @schema
    # -- args to add to the prefill deployment
    extraArgs: []

    # -- environment variables injected into each decode vLLM container
    env: []

  decode:
    # -- number of desired decode replicas
    replicas: 1

    # @schema
    # items:
    #   type: string
    # @schema
    # -- args to add to the decode deployment
    extraArgs: []

    # -- environment variables injected into each decode vLLM container
    env: []

  endpointPicker:

    # @schema
    # items:
    #   $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.EnvVar
    # @schema
    # -- Apply additional env variables to the endpoint picker deployment
    # <br /> Ref: https://github.com/neuralmagic/llm-d-inference-scheduler/blob/0.0.2/docs/architecture.md
    env: []

# -- Gateway configuration
# @default -- See below
gateway:

  # -- Deploy resources related to Gateway
  enabled: true

  # --  String to fully override gateway.fullname
  fullnameOverride: ""

  # -- String to partially override gateway.fullname
  nameOverride: ""

  # -- Gateway class that determines the backend used.
  # Currently supported values: "istio", "kgateway", "gke-l7-rilb", or "gke-l7-regional-external-managed"
  gatewayClassName: istio

  # @schema
  # additionalProperties: true
  # @schema
  # -- Additional annotations provided to the Gateway resource
  annotations: {}

  # Special parameters applied to kGateway via GatewayParameters resource
  kGatewayParameters:
    # @schema
    # type: [number, boolean]
    # @schema
    proxyUID: false

  # @schema
  # items:
  #  type: object
  #  properties:
  #    name:
  #      description: Name is the name of the Listener. This name MUST be unique within a Gateway
  #      type: string
  #    path:
  #      description: Path to expose via Ingress
  #      type: string
  #    port:
  #      description: Port is the network port. Multiple listeners may use the same port, subject to the Listener compatibility rules
  #      type: integer
  #      minimum: 1
  #      maximum: 65535
  #    protocol:
  #      description: Protocol specifies the network protocol this listener expects to receive
  #      type: string
  # @schema
  # Set of listeners exposed via the Gateway, also propagated to the Ingress if enabled
  listeners:
    - name: default
      path: /
      port: 80
      protocol: HTTP

  # -- Gateway's service type. Ingress is only available if the service type is set to NodePort. Accepted values: ["LoadBalancer", "NodePort"]
  serviceType: NodePort

# -- Ingress configuration
# @default -- See below
ingress:

  # -- Deploy Ingress
  enabled: true

  # -- Name of the IngressClass cluster resource which defines which controller will implement the resource (e.g nginx)
  ingressClassName: ""

  # @schema
  # additionalProperties: true
  # @schema
  # -- Additional annotations for the Ingress resource
  annotations: {}

  # -- Hostname to be used to expose the NodePort service to the inferencing gateway
  host: ""

  # -- List of additional hostnames to be covered with this ingress record (e.g. a CNAME)
  # <!-- E.g.
  # extraHosts:
  #   - name: llm-d.env.example.com
  #     path: / (Optional)
  #     pathType: Prefix (Optional)
  #     port: 7007 (Optional) -->
  extraHosts: []

  # -- Path to be used to expose the full route to access the inferencing gateway
  path: "/"

  # -- Ingress TLS parameters
  tls:

    # -- Enable TLS configuration for the host defined at `ingress.host` parameter
    enabled: false

    # -- The name to which the TLS Secret will be called
    secretName: ""

  # @schema
  # items:
  #   $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.networking.v1.IngressTLS
  # @schema
  # -- The TLS configuration for additional hostnames to be covered with this ingress record.
  # <br /> Ref: https://kubernetes.io/docs/concepts/services-networking/ingress/#tls
  # <!-- E.g.
  # extraTls:
  #   - hosts:
  #     - llm-d.env.example.com
  #     secretName: llm-d-env -->
  extraTls: []

  # -- used as part of the host dirivation if not specified from OCP cluster domain (dont edit)
  clusterRouterBase: ""

# -- Model service controller configuration
# @default -- See below
modelservice:

  # -- Toggle to deploy modelservice controller related resources
  enabled: true

  # -- Enable metrics gathering via podMonitor / ServiceMonitor
  metrics:

    # -- Enable metrics scraping from prefill and decode services, see `model
    enabled: true

    # -- Prometheus ServiceMonitor configuration
    # <br /> Ref: https://github.com/prometheus-operator/prometheus-operator/blob/main/Documentation/api-reference/api.md
    # @default -- See below
    serviceMonitor:

      # @schema
      # additionalProperties: true
      # @schema
      # -- Additional annotations provided to the ServiceMonitor
      annotations: {}

      # @schema
      # additionalProperties: true
      # @schema
      # -- Additional labels provided to the ServiceMonitor
      labels: {}

      # -- ServiceMonitor endpoint port
      port: "vllm"

      # -- ServiceMonitor endpoint path
      path: "/metrics"

      # -- ServiceMonitor endpoint interval at which metrics should be scraped
      interval: "15s"

      # -- ServiceMonitor namespace selector
      namespaceSelector:
        any: false

        # @schema
        # items:
        #   type: string
        # @schema
        matchNames: []

      # -- ServiceMonitor selector matchLabels
      # </br> matchLabels must match labels on modelservice Services
      selector:

        # @schema
        # $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.apimachinery.pkg.apis.meta.v1.LabelSelector
        # @schema
        matchLabels: {}


  # --  String to fully override modelservice.fullname
  fullnameOverride: ""

  # --  String to partially override modelservice.fullname
  nameOverride: ""

  # -- Number of controller replicas
  replicas: 1

  # -- Modelservice controller image, please change only if appropriate adjustments to the CRD are being made
  # @default -- See below
  image:

    # -- Model Service controller image registry
    registry: ghcr.io

    # -- Model Service controller image repository
    repository: llm-d/llm-d-model-service

    # -- Model Service controller image tag
    tag: "0.0.10"

    # -- Specify a imagePullPolicy
    imagePullPolicy: "Always"

    # @schema
    # items:
    #   type: string
    # @schema
    # -- Optionally specify an array of imagePullSecrets (evaluated as templates)
    pullSecrets: []

  # @schema
  # $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.SecurityContext
  # @schema
  # -- Security settings for a Pod.
  #  The security settings that you specify for a Pod apply to all Containers in the Pod.
  # <br /> Ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  podSecurityContext: {}

  # @schema
  # $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.SecurityContext
  # @schema
  # -- Security settings for a Container.
  # <br /> Ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container
  containerSecurityContext: {}

  # @schema
  # properties:
  #  nodeAffinity:
  #    $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.NodeAffinity
  #  podAffinity:
  #    $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.PodAffinity
  #  podAntiAffinity:
  #    $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.PodAntiAffinity
  # @schema
  # -- Affinity for pod assignment
  # <br /> Ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
  affinity: {}

  # @schema
  # items:
  #   $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.TopologySpreadConstraint
  # @schema
  # -- Topology Spread Constraints for pod assignment
  # <br /> Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#pod-topology-spread-constraints
  topologySpreadConstraints: []

  # @schema
  # additionalProperties: true
  # @schema
  # -- Node labels for pod assignment
  # <br /> Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#nodeselector
  nodeSelector: {}

  # @schema
  # items:
  #   $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.Toleration
  # @schema
  # -- Node tolerations for server scheduling to nodes with taints
  # <br /> Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/taint-and-toleration/
  tolerations: []

  # -- Endpoint picker configuration
  # @default -- See below
  epp:
    # -- Endpoint picker image used in ModelService CR presets
    # @default -- See below
    image:

      # -- Endpoint picker image registry
      registry: ghcr.io

      # -- Endpoint picker image repository
      repository: llm-d/llm-d-inference-scheduler

      # -- Endpoint picker image tag
      tag: 0.0.4

    # -- Specify a imagePullPolicy
      imagePullPolicy: "Always"

      # @schema
      # items:
      #   type: string
      # @schema
      # -- Optionally specify an array of imagePullSecrets (evaluated as templates)
      pullSecrets: []

    # @schema
    # $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.SecurityContext
    # @schema
    # -- Security settings for a Pod.
    #  The security settings that you specify for a Pod apply to all Containers in the Pod.
    # <br /> Ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
    podSecurityContext: {}

    # @schema
    # $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.SecurityContext
    # @schema
    # -- Security settings for a Container.
    # <br /> Ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container
    containerSecurityContext: {}

    # @schema
    # properties:
    #  nodeAffinity:
    #    $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.NodeAffinity
    #  podAffinity:
    #    $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.PodAffinity
    #  podAntiAffinity:
    #    $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.PodAntiAffinity
    # @schema
    # -- Affinity for pod assignment
    # <br /> Ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
    affinity: {}

    # @schema
    # items:
    #   $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.TopologySpreadConstraint
    # @schema
    # -- Topology Spread Constraints for pod assignment
    # <br /> Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#pod-topology-spread-constraints
    topologySpreadConstraints: []

    # @schema
    # additionalProperties: true
    # @schema
    # -- Node labels for pod assignment
    # <br /> Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#nodeselector
    nodeSelector: {}

    # @schema
    # items:
    #   $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.Toleration
    # @schema
    # -- Node tolerations for server scheduling to nodes with taints
    # <br /> Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/taint-and-toleration/
    tolerations: []


    # -- Enable metrics gathering via podMonitor / ServiceMonitor
    metrics:

      # -- Enable metrics scraping from endpoint picker service
      enabled: true

      # -- Prometheus ServiceMonitor configuration
      # <br /> Ref: https://github.com/prometheus-operator/prometheus-operator/blob/main/Documentation/api-reference/api.md
      # @default -- See below
      serviceMonitor:

        # @schema
        # additionalProperties: true
        # @schema
        # -- Additional annotations provided to the ServiceMonitor
        annotations: {}

        # @schema
        # additionalProperties: true
        # @schema
        # -- Additional labels provided to the ServiceMonitor
        labels: {}

        # -- ServiceMonitor endpoint port
        port: "metrics"

        # -- ServiceMonitor endpoint path
        path: "/metrics"

        # -- ServiceMonitor endpoint interval at which metrics should be scraped
        interval: "10s"

        # -- ServiceMonitor namespace selector
        namespaceSelector:
          any: false

          # @schema
          # items:
          #   type: string
          # @schema
          matchNames: []

        # -- ServiceMonitor selector matchLabels
        # </br> matchLabels must match labels on modelservice Services
        selector:

          # @schema
          # $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.apimachinery.pkg.apis.meta.v1.LabelSelector
          # @schema
          matchLabels: {}

    # -- Default environment variables for endpoint picker, use `defaultEnvVarsOverride` to override default behavior by defining the same variable again.
    # Ref: https://github.com/llm-d/llm-d-inference-scheduler/blob/main/docs/architecture.md#scorers--configuration
    defaultEnvVars:
      - name: ENABLE_KVCACHE_AWARE_SCORER
        value: "false"
      - name: KVCACHE_AWARE_SCORER_WEIGHT
        value: "1"
      - name: KVCACHE_INDEXER_REDIS_ADDR
        value: '{{ if .Values.redis.enabled }}{{ include "redis.master.service.fullurl" . }}{{ end }}'
      - name: ENABLE_PREFIX_AWARE_SCORER
        value: "true"
      - name: PREFIX_AWARE_SCORER_WEIGHT
        value: "2"
      - name: ENABLE_LOAD_AWARE_SCORER
        value: "true"
      - name: LOAD_AWARE_SCORER_WEIGHT
        value: "1"
      - name: ENABLE_SESSION_AWARE_SCORER
        value: "false"
      - name: SESSION_AWARE_SCORER_WEIGHT
        value: "1"
      - name: PD_ENABLED
        value: "false"
      - name: PD_PROMPT_LEN_THRESHOLD
        value: "10"
      - name: PREFILL_ENABLE_KVCACHE_AWARE_SCORER
        value: "false"
      - name: PREFILL_KVCACHE_AWARE_SCORER_WEIGHT
        value: "1"
      - name: PREFILL_KVCACHE_INDEXER_REDIS_ADDR
        value: '{{ if .Values.redis.enabled }}{{ include "redis.master.service.fullurl" . }}{{ end }}'
      - name: PREFILL_ENABLE_LOAD_AWARE_SCORER
        value: "false"
      - name: PREFILL_LOAD_AWARE_SCORER_WEIGHT
        value: "1"
      - name: PREFILL_ENABLE_PREFIX_AWARE_SCORER
        value: "false"
      - name: PREFILL_PREFIX_AWARE_SCORER_WEIGHT
        value: "1"
      - name: PREFILL_ENABLE_SESSION_AWARE_SCORER
        value: "false"
      - name: PREFILL_SESSION_AWARE_SCORER_WEIGHT
        value: "1"

    # @schema
    # items:
    #   $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.EnvVar
    # @schema
    # -- Override default environment variables for endpoint picker. This list has priorito over `defaultEnvVars`
    defaultEnvVarsOverride: []

  # -- Prefill options
  # @default -- See below
  prefill:

   # @schema
    # $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.SecurityContext
    # @schema
    # -- Security settings for a Pod.
    #  The security settings that you specify for a Pod apply to all Containers in the Pod.
    # <br /> Ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
    podSecurityContext: {}

    # @schema
    # $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.SecurityContext
    # @schema
    # -- Security settings for a Container.
    # <br /> Ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container
    containerSecurityContext: {}

    # @schema
    # properties:
    #  nodeAffinity:
    #    $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.NodeAffinity
    #  podAffinity:
    #    $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.PodAffinity
    #  podAntiAffinity:
    #    $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.PodAntiAffinity
    # @schema
    # -- Affinity for pod assignment
    # <br /> Ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
    affinity: {}

    # @schema
    # items:
    #   $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.TopologySpreadConstraint
    # @schema
    # -- Topology Spread Constraints for pod assignment
    # <br /> Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#pod-topology-spread-constraints
    topologySpreadConstraints: []

    # @schema
    # additionalProperties: true
    # @schema
    # -- Node labels for pod assignment
    # <br /> Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#nodeselector
    nodeSelector: {}

    # @schema
    # items:
    #   $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.Toleration
    # @schema
    # -- Node tolerations for server scheduling to nodes with taints
    # <br /> Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/taint-and-toleration/
    tolerations:

      # -- default NVIDIA GPU toleration
      - key: nvidia.com/gpu
        operator: Exists
        effect: NoSchedule

    # -- vLLM container settings
    vllm:

      # @schema
      # additionalProperties: true
      # $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.SecurityContext
      # @schema
      # -- Security settings for a Container.
      # <br /> Ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container
      containerSecurityContext:
          allowPrivilegeEscalation: false

  # -- Decode options
  # @default -- See below
  decode:

   # @schema
    # $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.SecurityContext
    # @schema
    # -- Security settings for a Pod.
    #  The security settings that you specify for a Pod apply to all Containers in the Pod.
    # <br /> Ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
    podSecurityContext: {}

    # @schema
    # $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.SecurityContext
    # @schema
    # -- Security settings for a Container.
    # <br /> Ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container
    containerSecurityContext: {}

    # @schema
    # properties:
    #  nodeAffinity:
    #    $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.NodeAffinity
    #  podAffinity:
    #    $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.PodAffinity
    #  podAntiAffinity:
    #    $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.PodAntiAffinity
    # @schema
    # -- Affinity for pod assignment
    # <br /> Ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
    affinity: {}

    # @schema
    # items:
    #   $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.TopologySpreadConstraint
    # @schema
    # -- Topology Spread Constraints for pod assignment
    # <br /> Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#pod-topology-spread-constraints
    topologySpreadConstraints: []

    # @schema
    # additionalProperties: true
    # @schema
    # -- Node labels for pod assignment
    # <br /> Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#nodeselector
    nodeSelector: {}

    # @schema
    # items:
    #   $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.Toleration
    # @schema
    # -- Node tolerations for server scheduling to nodes with taints
    # <br /> Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/taint-and-toleration/
    tolerations:

      # -- default NVIDIA GPU toleration
      - key: nvidia.com/gpu
        operator: Exists
        effect: NoSchedule

    # -- vLLM container settings
    vllm:

      # @schema
      # additionalProperties: true
      # $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.SecurityContext
      # @schema
      # -- Security settings for a Container.
      # <br /> Ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container
      containerSecurityContext:
        securityContext:
          capabilities:
            drop:
              - MKNOD
          allowPrivilegeEscalation: false

  # -- vLLM container options
  # @default -- See below
  vllm:

    # -- vLLM image used in ModelService CR presets
    # @default -- See below
    image:

      # -- llm-d image registry
      registry: ghcr.io

      # -- llm-d image repository
      repository: llm-d/llm-d

      # -- llm-d image tag
      tag: 0.0.8

      # -- Specify a imagePullPolicy
      imagePullPolicy: "IfNotPresent"

      # @schema
      # items:
      #   type: string
      # @schema
      # -- Optionally specify an array of imagePullSecrets (evaluated as templates)
      pullSecrets: []

    # -- Enable metrics gathering via podMonitor / ServiceMonitor
    metrics:

      # -- Enable metrics scraping from prefill & decode services
      enabled: true

    # -- Log level to run VLLM with
    # <br /> VLLM supports standard python log-levels, see: https://docs.python.org/3/library/logging.html#logging-levels
    # <br /> Options: "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"
    logLevel: "INFO"

  # -- Routing proxy container options
  # @default -- See below
  routingProxy:

    # -- Routing proxy image used in ModelService CR presets
    image:

      # -- Routing proxy image registry
      registry: ghcr.io

      # -- Routing proxy image repository
      repository: llm-d/llm-d-routing-sidecar

      # -- Routing proxy image tag
      tag: "0.0.6"

      # -- Specify a imagePullPolicy
      imagePullPolicy: "IfNotPresent"

      # @schema
      # items:
      #   type: string
      # @schema
      # -- Optionally specify an array of imagePullSecrets (evaluated as templates)
      pullSecrets: []

    # @schema
    # $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.SecurityContext
    # @schema
    # -- Security settings for a Container.
    # <br /> Ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container
    containerSecurityContext: {}

  # -- llm-d inference simulator container options
  # @default -- See below
  inferenceSimulator:

    # -- llm-d inference simulator image used in ModelService CR presets
    # @default -- See below
    image:

      # -- llm-d inference simulator image registry
      registry: ghcr.io

      # -- llm-d inference simulator image repository
      repository: llm-d/llm-d-inference-sim

      # -- llm-d inference simulator image tag
      tag: "0.0.4"

      # -- Specify a imagePullPolicy
      imagePullPolicy: "IfNotPresent"

      # @schema
      # items:
      #   type: string
      # @schema
      # -- Optionally specify an array of imagePullSecrets (evaluated as templates)
      pullSecrets: []

    # @schema
    # $ref: https://raw.githubusercontent.com/yannh/kubernetes-json-schema/master/master/_definitions.json#/definitions/io.k8s.api.core.v1.SecurityContext
    # @schema
    # -- Security settings for a Container.
    # <br /> Ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container
    containerSecurityContext: {}

  # @schema
  # additionalProperties: true
  # @schema
  # -- Annotations to add to all modelservice resources
  annotations: {}

  # @schema
  # additionalProperties: true
  # @schema
  # -- Pod annotations for modelservice
  podAnnotations: {}

  # @schema
  # additionalProperties: true
  # @schema
  # -- Pod labels for modelservice
  podLabels: {}

  # Model service controller settings
  service:

    # -- Toggle to deploy a Service resource for Model service controller
    enabled: true

    # -- Port number exposed from Model Service controller
    port: 8443

    # -- Service type
    type: ClusterIP

  # -- Service Account Configuration
  # @default -- See below
  serviceAccount:

    # -- Enable the creation of a ServiceAccount for Modelservice pods
    create: true

    # --  String to fully override modelservice.serviceAccountName, defaults to modelservice.fullname
    fullnameOverride: ""

    # --  String to partially override modelservice.serviceAccountName, defaults to modelservice.fullname
    nameOverride: ""

    # @schema
    # additionalProperties: true
    # @schema
    # -- Additional custom labels to the service ServiceAccount.
    labels: {}

    # @schema
    # additionalProperties: true
    # @schema
    # -- Additional custom annotations for the ServiceAccount.
    annotations: {}

  rbac:
    # -- Enable the creation of RBAC resources
    create: true

# @schema
# $ref: https://raw.githubusercontent.com/bitnami/charts/refs/tags/redis/20.13.4/bitnami/redis/values.schema.json
# @schema
# -- Bitnami/Redis chart configuration
# @default -- Use sane defaults for minimal Redis deployment
redis:
  enabled: true
  auth:
    enabled: false
    existingSecretPasswordKey: ""
    existingSecret: ""
  architecture: standalone
  image:
    registry: quay.io
    repository: sclorg/redis-7-c9s
    tag: c9s
  master:
    kind: Deployment
    resources:
      limits:
        memory: "256Mi"
        cpu: "250m"
      requests:
        memory: "128Mi"
        cpu: "100m"
    persistence:
      enabled: false
    pdb:
      create: false
    service:
      ports:
        redis: 8100
  networkPolicy:
    enabled: false
