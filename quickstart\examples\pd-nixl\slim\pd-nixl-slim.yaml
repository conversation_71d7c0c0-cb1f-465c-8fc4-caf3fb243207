# Validated to run with minikube on a single Nvidia L4 with 32G of RAM. EC2 type: g6.2xlarge
# NOTE: Run with --disable-metrics-collection to prevent exhausting RAM due to cpu offloading on an L4
sampleApplication:
  baseConfigMapRefName: basic-gpu-with-nixl-preset
  model:
    modelArtifactURI: hf://openai-community/gpt2
    modelName: "openai-community/gpt2"
  resources:
    limits:
    requests:
  prefill:
    replicas: 1
    extraArgs:
      - "--gpu-memory-utilization"
      - "0.2"
      - "--cpu-offload-gb"
      - "6"
  decode:
    replicas: 1
    extraArgs:
      - "--gpu-memory-utilization"
      - "0.3"
      - "--cpu-offload-gb"
      - "6"
redis:
  enabled: false
modelservice:
  epp:
    defaultEnvVarsOverride:
      - name: ENABLE_KVCACHE_AWARE_SCORER
        value: "false"
      - name: ENABLE_PREFIX_AWARE_SCORER
        value: "true"
      - name: ENABLE_LOAD_AWARE_SCORER
        value: "true"
      - name: ENABLE_SESSION_AWARE_SCORER
        value: "false"
      - name: PD_ENABLED
        value: "true"
      - name: PD_PROMPT_LEN_THRESHOLD
        value: "10"
      - name: PREFILL_ENABLE_KVCACHE_AWARE_SCORER
        value: "false"
      - name: PREFILL_ENABLE_LOAD_AWARE_SCORER
        value: "true"
      - name: PREFILL_ENABLE_PREFIX_AWARE_SCORER
        value: "true"
      - name: PREFILL_ENABLE_SESSION_AWARE_SCORER
        value: "false"
