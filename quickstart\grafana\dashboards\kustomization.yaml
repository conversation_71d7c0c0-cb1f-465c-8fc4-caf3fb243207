apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

# This namespace must match the Subject namespaces for the RBAC in ./openshift/grafana/instance-w-prom-ds/rbac.yaml
namespace: llm-d-observability

resources:
- llm-d-dashboard.yaml
- inference-gateway-dashboard.yaml

# generate a ConfigMap from local JSON file
configMapGenerator:
- name: llm-d-dashboard-json
  files:
    # Format: key=path
    - dashboard.json=llm-d-dashboard.json

generatorOptions:
  # so you don’t get a random suffix on the CM name
  disableNameSuffixHash: true
