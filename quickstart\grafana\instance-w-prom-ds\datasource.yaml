apiVersion: grafana.integreatly.org/v1beta1
kind: GrafanaDatasource
metadata:
  name: prometheus
spec:
  instanceSelector:
    matchLabels:
      app: grafana
  datasource:
    name: prometheus
    access: proxy
    editable: true
    type: prometheus
    url: "https://thanos-querier.openshift-monitoring.svc.cluster.local:9091"
    isDefault: true
    secureJsonData:
      "httpHeaderValue1": "Bearer ${token}"
    jsonData:
      "httpHeaderName1": "Authorization"
      "timeInterval": "5s"
      "tlsSkipVerify": true
  valuesFrom:
    - targetPath: "secureJsonData.httpHeaderValue1"
      valueFrom:
        secretKeyRef:
          name: "grafana-sa-token"
          key: "token"
