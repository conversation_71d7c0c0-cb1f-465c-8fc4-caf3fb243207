---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: view-grafana
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: view
subjects:
  - kind: ServiceAccount
    name: grafana-sa-token
    namespace: llm-d-observability
  - kind: ServiceAccount
    name: grafana-sa
    namespace: llm-d-observability
---
# This ClusterRoleBinding allows grafana-sa-token SA to access metrics from the OpenShift user
# workload monitoring stack.
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: cluster-monitoring-view-grafana
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-monitoring-view
subjects:
  - kind: ServiceAccount
    name: grafana-sa-token
    namespace: llm-d-observability
  - kind: ServiceAccount
    name: grafana-sa
    namespace: llm-d-observability
---
# This ClusterRoleBinding allows grafana-sa-token SA to access metrics from the built-in OpenShift
# cluster monitoring stack, required for accessing core OpenShift metrics and monitoring data
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: openshift-cluster-monitoring-view-grafana
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: openshift-cluster-monitoring-view
subjects:
  - kind: ServiceAccount
    name: grafana-sa-token
    namespace: llm-d-observability
  - kind: ServiceAccount
    name: grafana-sa
    namespace: llm-d-observability
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: grafana-prometheus-reader
rules:
- apiGroups: ["*"]
  resources:
    - "prometheuses"
    - "prometheuses/*"
  verbs:
    - "get"
    - "list"
    - "watch"
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: grafana-prometheus-reader-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: grafana-prometheus-reader
subjects:
- kind: ServiceAccount
  name: grafana-sa-token
  namespace: llm-d-observability
- kind: ServiceAccount
  name: grafana-sa
  namespace: llm-d-observability
